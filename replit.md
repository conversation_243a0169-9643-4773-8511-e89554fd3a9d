# Virtual Machine Management System

## Overview

This is a full-stack virtual machine management system built with React, Express, and TypeScript. The application provides a comprehensive dashboard for managing virtual machines, hypervisors, storage pools, storage volumes, and virtual networks. It features enterprise-scale infrastructure management with support for 400+ KVM hypervisors, automatic Oracle Cloud Infrastructure (OCI) integration, qemu+ssh connections, and real-time monitoring capabilities.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **UI Components**: Radix UI primitives with shadcn/ui design system
- **Styling**: Tailwind CSS with CSS variables for theming
- **State Management**: TanStack Query (React Query) for server state management
- **Routing**: Wouter for client-side routing
- **Form Handling**: React Hook Form with Zod validation
- **Build Tool**: Vite with custom configuration for development and production

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **API Design**: RESTful API with structured error handling
- **Data Storage**: In-memory storage implementation with interface abstraction
- **Schema Validation**: Zod schemas for request/response validation
- **Development**: Hot module replacement via Vite integration in development mode

### Data Models
The system manages four core entities:
- **Virtual Machines**: CPU, memory, disk resources with status tracking
- **Storage Pools**: Different storage types (directory, LVM, NFS) with capacity management
- **Storage Volumes**: Individual storage units linked to VMs with format support
- **Networks**: Virtual network configurations with different modes (NAT, bridge, isolated)
- **Activities**: System activity logging for audit trails

### Database Design
- **Database**: PostgreSQL with persistent storage (migrated from in-memory)
- **ORM**: Drizzle ORM configured for PostgreSQL with Neon serverless integration
- **Schema**: Centralized schema definitions in `/shared/schema.ts` with hypervisor connection settings
- **Migration**: Database migrations managed through Drizzle Kit (`npm run db:push`)
- **Seeding**: Enterprise-scale sample data with 20 hypervisors and 247+ VMs

### Hypervisor Management
- **Connection Types**: qemu+ssh, qemu+tcp, qemu+tls support
- **OCI Integration**: Automatic discovery and import of Oracle Cloud compute instances
- **Manual Addition**: SSH-based hypervisor configuration with key authentication
- **Monitoring**: Real-time status monitoring, resource tracking, and connection testing
- **Enterprise Scale**: Designed to handle 400+ hypervisors across multiple data centers

### Component Architecture
- **Layout**: Responsive sidebar navigation with tab-based content switching
- **Data Display**: Comprehensive tables and cards for resource visualization
- **Interactive Elements**: Modal dialogs for VM creation, confirmation dialogs for destructive actions
- **Real-time Updates**: Automatic data refetching and optimistic updates

### Development Workflow
- **Hot Reloading**: Vite development server with Express middleware integration
- **Type Safety**: Shared TypeScript types between client and server
- **Code Organization**: Clear separation between client, server, and shared code
- **Asset Management**: Static asset serving with proper caching headers

## External Dependencies

### UI and Styling
- **Radix UI**: Comprehensive set of accessible React components
- **Tailwind CSS**: Utility-first CSS framework with PostCSS processing
- **Lucide React**: Icon library for consistent iconography
- **Class Variance Authority**: Component variant management
- **shadcn/ui**: Pre-built component library built on Radix UI

### Database and ORM
- **Neon Database**: Serverless PostgreSQL database hosting
- **Drizzle ORM**: Type-safe SQL ORM with PostgreSQL dialect
- **Drizzle Kit**: Database migration and schema management tools

### Development Tools
- **Vite**: Fast build tool with HMR and optimized production builds
- **TypeScript**: Static type checking across the entire stack
- **ESBuild**: Fast JavaScript bundler for server-side code
- **TSX**: TypeScript execution engine for development

### State Management and Data Fetching
- **TanStack Query**: Server state management with caching, background updates, and optimistic updates
- **React Hook Form**: Form state management with validation
- **Zod**: Runtime type validation and schema definition

### Routing and Navigation
- **Wouter**: Lightweight client-side routing library
- **React Router**: Alternative routing solution (available but not currently used)