import { db } from "../server/db";
import { hypervisors, virtualMachines, storagePools, storageVolumes, networks, activities } from "@shared/schema";

async function seedDatabase() {
  console.log("🌱 Seeding database with sample data...");

  try {
    // Create sample hypervisors simulating a large-scale deployment
    const locations = ["DC1-Rack01", "DC1-Rack02", "DC1-Rack03", "DC2-Rack01", "DC2-Rack02"];
    const baseHypervisors = [
      {
        name: "kvm-host-001",
        hostname: "kvm001.datacenter.local", 
        ipAddress: "*********",
        status: "maintenance" as const,
        hypervisorType: "kvm" as const,
        version: "6.2.0",
        cpuCores: 32,
        cpuUsage: 87,
        memoryGB: 128,
        memoryUsage: 46,
        totalVMs: 12,
        runningVMs: 6,
        location: "DC1-Rack01"
      },
      {
        name: "kvm-host-002",
        hostname: "kvm002.datacenter.local",
        ipAddress: "*********", 
        status: "online" as const,
        hypervisorType: "kvm" as const,
        version: "6.2.0",
        cpuCores: 48,
        cpuUsage: 34,
        memoryGB: 256,
        memoryUsage: 67,
        totalVMs: 18,
        runningVMs: 15,
        location: "DC1-Rack01"
      },
      {
        name: "kvm-host-003",
        hostname: "kvm003.datacenter.local",
        ipAddress: "*********",
        status: "online" as const,
        hypervisorType: "kvm" as const,
        version: "6.1.5",
        cpuCores: 64,
        cpuUsage: 23,
        memoryGB: 512,
        memoryUsage: 45,
        totalVMs: 24,
        runningVMs: 20,
        location: "DC1-Rack02"
      }
    ];

    // Scale up to 20 hypervisors for demo
    const allHypervisors = [];
    for (let i = 0; i < 20; i++) {
      const base = baseHypervisors[i % baseHypervisors.length];
      const hypervisor = {
        ...base,
        name: `kvm-host-${String(i + 1).padStart(3, '0')}`,
        hostname: `kvm${String(i + 1).padStart(3, '0')}.datacenter.local`,
        ipAddress: `10.1.${Math.floor(i / 254) + 1}.${(i % 254) + 10}`,
        location: locations[i % locations.length],
        status: i % 10 === 0 ? "maintenance" as const : "online" as const,
        cpuUsage: Math.floor(Math.random() * 80) + 10,
        memoryUsage: Math.floor(Math.random() * 70) + 20,
        totalVMs: Math.floor(Math.random() * 15) + 5,
        runningVMs: Math.floor(Math.random() * 10) + 2,
      };
      allHypervisors.push(hypervisor);
    }

    console.log(`Creating ${allHypervisors.length} hypervisors...`);
    const createdHypervisors = await db.insert(hypervisors).values(allHypervisors).returning();

    // Create VMs for each hypervisor
    const vmTemplates = [
      { name: "web-server", os: "Ubuntu 22.04", cpuCores: 2, memoryMB: 4096, diskGB: 40 },
      { name: "database", os: "CentOS 8", cpuCores: 4, memoryMB: 8192, diskGB: 100 },
      { name: "dev-environment", os: "Ubuntu 20.04", cpuCores: 2, memoryMB: 2048, diskGB: 30 },
      { name: "windows-server", os: "Windows Server 2019", cpuCores: 4, memoryMB: 8192, diskGB: 80 },
      { name: "test-vm", os: "Debian 11", cpuCores: 1, memoryMB: 1024, diskGB: 20 },
    ];

    const allVMs = [];
    for (const hypervisor of createdHypervisors) {
      const vmCount = hypervisor.totalVMs;
      for (let i = 0; i < vmCount; i++) {
        const template = vmTemplates[i % vmTemplates.length];
        const statuses = ["running", "stopped", "paused"] as const;
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        
        const vm = {
          name: `${template.name}-${hypervisor.name.split('-')[2]}-${String(i + 1).padStart(2, '0')}`,
          hypervisorId: hypervisor.id,
          os: template.os,
          status,
          cpuCores: template.cpuCores,
          cpuUsage: status === "running" ? Math.floor(Math.random() * 80) + 5 : 0,
          memoryMB: template.memoryMB,
          memoryUsage: status === "running" ? Math.floor(Math.random() * 70) + 10 : 0,
          diskGB: template.diskGB,
          diskUsage: Math.floor(Math.random() * 60) + 20,
          vmType: "guest" as const,
          autostart: Math.random() > 0.7,
        };
        allVMs.push(vm);
      }
    }

    console.log(`Creating ${allVMs.length} virtual machines...`);
    await db.insert(virtualMachines).values(allVMs);

    // Create storage pools
    const poolData = [
      { name: "default", type: "directory", path: "/var/lib/libvirt/images", capacityGB: 1000, usedGB: 250 },
      { name: "ssd-pool", type: "lvm", path: "/dev/vg-ssd/pool", capacityGB: 500, usedGB: 120 },
      { name: "backup-storage", type: "nfs", path: "nfs-server:/backups", capacityGB: 2000, usedGB: 800 },
    ];

    console.log("Creating storage pools...");
    const createdPools = await db.insert(storagePools).values(poolData).returning();

    // Create storage volumes
    const volumeData = [];
    for (const pool of createdPools) {
      for (let i = 0; i < 5; i++) {
        volumeData.push({
          name: `${pool.name}-volume-${i + 1}`,
          poolId: pool.id,
          sizeGB: Math.floor(Math.random() * 100) + 20,
          format: i % 2 === 0 ? "qcow2" as const : "raw" as const,
        });
      }
    }

    console.log("Creating storage volumes...");
    await db.insert(storageVolumes).values(volumeData);

    // Create networks
    const networkData = [
      { name: "default", mode: "nat", ipRange: "*************/24", dhcpRange: "***************-***************", bridge: "virbr0" },
      { name: "production", mode: "bridge", ipRange: "********/24", bridge: "br0" },
      { name: "isolated-test", mode: "isolated", ipRange: "**********/24", dhcpRange: "************-************" },
    ];

    console.log("Creating networks...");
    await db.insert(networks).values(networkData);

    // Create activities
    const activityData = [
      { message: 'Hypervisor "kvm-host-001" entered maintenance mode', type: "warning" },
      { message: 'VM "web-server-001-01" started successfully', type: "success" },
      { message: 'Storage pool "ssd-pool" running low on space', type: "warning" },
      { message: 'Network "production" configured', type: "info" },
      { message: 'System backup completed successfully', type: "success" },
    ];

    console.log("Creating activity logs...");
    await db.insert(activities).values(activityData);

    console.log("✅ Database seeding completed successfully!");
    console.log(`Created ${createdHypervisors.length} hypervisors, ${allVMs.length} VMs, ${poolData.length} storage pools, and more.`);

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

// Run the seed if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { seedDatabase };