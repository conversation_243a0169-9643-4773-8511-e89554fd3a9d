import { useQuery } from "@tanstack/react-query";
import type { StoragePool, StorageVolume } from "@shared/schema";

export function useStorage() {
  const poolsQuery = useQuery<StoragePool[]>({
    queryKey: ["/api/storage/pools"],
  });

  const volumesQuery = useQuery<StorageVolume[]>({
    queryKey: ["/api/storage/volumes"],
  });

  return {
    data: {
      pools: poolsQuery.data,
      volumes: volumesQuery.data,
    },
    isLoading: poolsQuery.isLoading || volumesQuery.isLoading,
    error: poolsQuery.error || volumesQuery.error,
  };
}
