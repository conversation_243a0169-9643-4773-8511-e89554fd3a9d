import { useState, useEffect, createContext, useContext } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequestJson } from "@/lib/queryClient";
import type { User } from "@shared/schema";

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string, user: User) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export function useAuthProvider() {
  const [token, setToken] = useState<string | null>(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("auth_token");
    }
    return null;
  });

  const queryClient = useQueryClient();

  // Query to get current user when token exists
  const { data: user, isLoading } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    enabled: !!token,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const login = (newToken: string, newUser: User) => {
    setToken(newToken);
    localStorage.setItem("auth_token", newToken);
    
    // Set the user data in the cache
    queryClient.setQueryData(["/api/auth/user"], newUser);
  };

  const logout = async () => {
    try {
      // Call logout endpoint if token exists
      if (token) {
        await apiRequestJson("/api/auth/logout", "POST");
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setToken(null);
      localStorage.removeItem("auth_token");
      queryClient.clear();
    }
  };

  // Update query client's default options to include auth header
  useEffect(() => {
    if (token) {
      queryClient.setDefaultOptions({
        queries: {
          retry: (failureCount, error: any) => {
            // Don't retry on 401 errors
            if (error?.message?.includes("401")) {
              return false;
            }
            return failureCount < 3;
          },
        },
      });
    }
  }, [token, queryClient]);

  return {
    user: user || null,
    token,
    isAuthenticated: !!token && !!user,
    isLoading: !!token && isLoading,
    login,
    logout,
  };
}

export { AuthContext };