@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(224, 71.4%, 4.1%);
  --muted: hsl(220, 14.3%, 95.9%);
  --muted-foreground: hsl(220, 8.9%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(224, 71.4%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(224, 71.4%, 4.1%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: hsl(221.2, 83.2%, 53.3%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(220, 14.3%, 95.9%);
  --secondary-foreground: hsl(220.9, 39.3%, 11%);
  --accent: hsl(220, 14.3%, 95.9%);
  --accent-foreground: hsl(220.9, 39.3%, 11%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(224, 71.4%, 4.1%);
  --radius: 0.5rem;
}

.dark {
  --background: hsl(224, 71.4%, 4.1%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(223, 47%, 11%);
  --muted-foreground: hsl(215.4, 16.3%, 56.9%);
  --popover: hsl(224, 71.4%, 4.1%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(224, 71.4%, 4.1%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(216, 12.2%, 16.9%);
  --input: hsl(216, 12.2%, 16.9%);
  --primary: hsl(210, 40%, 98%);
  --primary-foreground: hsl(222.2, 84%, 4.9%);
  --secondary: hsl(223, 47%, 11%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(223, 47%, 11%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(216, 12.2%, 16.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}
