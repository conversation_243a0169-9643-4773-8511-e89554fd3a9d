import { useState } from "react";
import { Sidebar } from "@/components/sidebar";
import { OverviewStats } from "@/components/overview-stats";
import { VmTable } from "@/components/vm-table";
import { StorageManagement } from "@/components/storage-management";
import { NetworkManagement } from "@/components/network-management";
import { VmCreateWizard } from "@/components/vm-create-wizard";
import { HypervisorManagement } from "@/components/hypervisor-management";
import { Button } from "@/components/ui/button";
import { RefreshCw, Plus } from "lucide-react";
import { useVMs } from "@/hooks/use-vms";

export type TabType = "overview" | "hypervisors" | "machines" | "storage" | "networks" | "create";

const tabConfig = {
  overview: { title: "Dashboard Overview", subtitle: "Monitor and manage your infrastructure" },
  hypervisors: { title: "Hypervisor Management", subtitle: "Manage KVM hosts and hypervisors" },
  machines: { title: "Virtual Machines", subtitle: "Manage your VM instances" },
  storage: { title: "Storage Management", subtitle: "Configure storage pools and volumes" },
  networks: { title: "Network Configuration", subtitle: "Manage virtual networks" },
  create: { title: "Create Virtual Machine", subtitle: "Set up a new VM instance" },
};

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState<TabType>("overview");
  const { refetch: refetchVMs } = useVMs();

  const handleRefresh = () => {
    refetchVMs();
  };

  const handleCreateVM = () => {
    setActiveTab("create");
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return <OverviewStats onCreateVM={handleCreateVM} />;
      case "hypervisors":
        return <HypervisorManagement />;
      case "machines":
        return <VmTable />;
      case "storage":
        return <StorageManagement />;
      case "networks":
        return <NetworkManagement />;
      case "create":
        return <VmCreateWizard onComplete={() => setActiveTab("machines")} />;
      default:
        return <OverviewStats onCreateVM={handleCreateVM} />;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-slate-200 px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-slate-800">
                {tabConfig[activeTab].title}
              </h2>
              <p className="text-sm text-slate-600 mt-1">
                {tabConfig[activeTab].subtitle}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button onClick={handleCreateVM} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                New VM
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleRefresh}
                className="text-slate-400 hover:text-slate-600"
              >
                <RefreshCw className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="p-6">
            {renderTabContent()}
          </div>
        </main>
      </div>
    </div>
  );
}
