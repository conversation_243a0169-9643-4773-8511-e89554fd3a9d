import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Dashboard from "@/pages/dashboard";
import NotFound from "@/pages/not-found";
import { LoginForm } from "@/components/login-form";
import { UserProfile } from "@/components/user-profile";
import { AuthContext, useAuthProvider } from "@/hooks/use-auth";
import { useState } from "react";

function Router() {
  const authProvider = useAuthProvider();
  const { isAuthenticated, isLoading, user, logout, login } = authProvider;
  const [showProfile, setShowProfile] = useState(false);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <LoginForm 
        onLoginSuccess={(token, userData) => {
          login(token, userData);
        }} 
      />
    );
  }

  if (showProfile && user) {
    return (
      <UserProfile 
        user={user} 
        onLogout={() => {
          logout();
          setShowProfile(false);
        }} 
      />
    );
  }

  return (
    <AuthContext.Provider value={authProvider}>
      <div className="min-h-screen bg-slate-50">
        {/* Navigation Header */}
        <div className="bg-white border-b border-slate-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-slate-900">VM Manager</h1>
              <div className="text-sm text-slate-600">
                Welcome back, {user?.firstName || user?.username}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowProfile(true)}
                className="text-sm text-slate-600 hover:text-slate-900 transition-colors"
              >
                Profile
              </button>
              <button
                onClick={logout}
                className="text-sm text-slate-600 hover:text-slate-900 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Switch>
          <Route path="/" component={Dashboard} />
          <Route component={NotFound} />
        </Switch>
      </div>
    </AuthContext.Provider>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
