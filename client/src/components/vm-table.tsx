import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Monitor, Play, Pause, Square, Edit, Trash2, RotateCcw } from "lucide-react";
import { useVMs } from "@/hooks/use-vms";
import { ConfirmationDialog } from "@/components/confirmation-dialog";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import type { VirtualMachine } from "@shared/schema";

export function VmTable() {
  const { data: vms, isLoading, refetch } = useVMs();
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({ open: false, title: "", message: "", onConfirm: () => {} });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const vmOperationMutation = useMutation({
    mutationFn: async ({ vmId, action }: { vmId: string; action: string }) => {
      const response = await apiRequest("POST", `/api/vms/${vmId}/operations`, { action });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/vms"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      queryClient.invalidateQueries({ queryKey: ["/api/activities"] });
      toast({ title: "Operation completed successfully" });
    },
    onError: () => {
      toast({ title: "Operation failed", variant: "destructive" });
    },
  });

  const deleteVmMutation = useMutation({
    mutationFn: async (vmId: string) => {
      await apiRequest("DELETE", `/api/vms/${vmId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/vms"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      queryClient.invalidateQueries({ queryKey: ["/api/activities"] });
      toast({ title: "VM deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete VM", variant: "destructive" });
    },
  });

  const handleVmOperation = (vm: VirtualMachine, action: string) => {
    const confirmActions = ["stop", "restart"];
    
    if (confirmActions.includes(action)) {
      const actionMessages = {
        stop: { title: "Stop Virtual Machine", message: "Are you sure you want to stop this VM? Any unsaved work will be lost." },
        restart: { title: "Restart Virtual Machine", message: "Are you sure you want to restart this VM? Any unsaved work will be lost." },
      };
      
      const config = actionMessages[action as keyof typeof actionMessages];
      setConfirmDialog({
        open: true,
        title: config.title,
        message: config.message,
        onConfirm: () => vmOperationMutation.mutate({ vmId: vm.id, action }),
      });
    } else {
      vmOperationMutation.mutate({ vmId: vm.id, action });
    }
  };

  const handleDeleteVm = (vm: VirtualMachine) => {
    setConfirmDialog({
      open: true,
      title: "Delete Virtual Machine",
      message: `Are you sure you want to delete "${vm.name}"? This action cannot be undone and all data will be lost.`,
      onConfirm: () => deleteVmMutation.mutate(vm.id),
    });
  };

  const handleOpenConsole = (vm: VirtualMachine) => {
    if (vm.status !== "running") {
      toast({ title: "VM must be running to access console", variant: "destructive" });
      return;
    }
    // In a real implementation, this would open a VNC/SPICE console
    window.open(`/console/${vm.id}`, "_blank", "width=800,height=600");
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { variant: "default" as const, className: "bg-green-100 text-green-800 border-green-200" },
      paused: { variant: "secondary" as const, className: "bg-amber-100 text-amber-800 border-amber-200" },
      stopped: { variant: "destructive" as const, className: "bg-red-100 text-red-800 border-red-200" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.stopped;
    
    return (
      <Badge variant={config.variant} className={config.className}>
        <div className={`w-1.5 h-1.5 rounded-full mr-1 ${
          status === "running" ? "bg-green-500" :
          status === "paused" ? "bg-amber-500" : "bg-red-500"
        }`}></div>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getAvailableActions = (vm: VirtualMachine) => {
    switch (vm.status) {
      case "running":
        return [
          { icon: Monitor, action: "console", label: "Console", variant: "ghost" as const },
          { icon: Pause, action: "pause", label: "Pause", variant: "ghost" as const },
          { icon: RotateCcw, action: "restart", label: "Restart", variant: "ghost" as const },
          { icon: Square, action: "stop", label: "Stop", variant: "ghost" as const },
        ];
      case "paused":
        return [
          { icon: Play, action: "resume", label: "Resume", variant: "ghost" as const },
          { icon: Square, action: "stop", label: "Stop", variant: "ghost" as const },
        ];
      case "stopped":
        return [
          { icon: Play, action: "start", label: "Start", variant: "ghost" as const },
          { icon: Edit, action: "edit", label: "Edit", variant: "ghost" as const },
          { icon: Trash2, action: "delete", label: "Delete", variant: "ghost" as const },
        ];
      default:
        return [];
    }
  };

  if (isLoading) {
    return <div>Loading virtual machines...</div>;
  }

  return (
    <>
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-slate-900">Virtual Machines</h3>
            <Button variant="ghost" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          </div>
        </div>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">CPU</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Memory</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Disk</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {vms?.map((vm) => (
                  <tr key={vm.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Monitor className="h-5 w-5 text-slate-400 mr-3" />
                        <div>
                          <div className="text-sm font-medium text-slate-900">{vm.name}</div>
                          <div className="text-sm text-slate-500">{vm.os}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(vm.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                      {vm.cpuCores} vCPUs ({vm.cpuUsage}%)
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                      {(vm.memoryMB / 1024).toFixed(1)} GB ({vm.memoryUsage}%)
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                      {vm.diskGB} GB ({vm.diskUsage}%)
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-1">
                        {getAvailableActions(vm).map((actionConfig) => {
                          const Icon = actionConfig.icon;
                          return (
                            <Button
                              key={actionConfig.action}
                              variant={actionConfig.variant}
                              size="sm"
                              onClick={() => {
                                if (actionConfig.action === "console") {
                                  handleOpenConsole(vm);
                                } else if (actionConfig.action === "delete") {
                                  handleDeleteVm(vm);
                                } else if (actionConfig.action === "edit") {
                                  toast({ title: "Edit functionality not implemented yet" });
                                } else {
                                  handleVmOperation(vm, actionConfig.action);
                                }
                              }}
                              disabled={vmOperationMutation.isPending || deleteVmMutation.isPending}
                            >
                              <Icon className="h-4 w-4" />
                            </Button>
                          );
                        })}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <ConfirmationDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={confirmDialog.onConfirm}
        onCancel={() => setConfirmDialog({ ...confirmDialog, open: false })}
      />
    </>
  );
}
