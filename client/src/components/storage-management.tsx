import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Plus, HardDrive } from "lucide-react";
import { useStorage } from "@/hooks/use-storage";

export function StorageManagement() {
  const { data: { pools, volumes } = { pools: [], volumes: [] }, isLoading } = useStorage();

  if (isLoading) {
    return <div>Loading storage information...</div>;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Storage Pools */}
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-slate-900">Storage Pools</h3>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-1" />
              Add Pool
            </Button>
          </div>
        </div>
        <CardContent className="p-0">
          <div className="divide-y divide-slate-200">
            {pools?.map((pool) => (
              <div key={pool.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <HardDrive className="h-6 w-6 text-blue-500 mr-3" />
                    <div>
                      <h4 className="text-sm font-medium text-slate-900">{pool.name}</h4>
                      <p className="text-sm text-slate-500">{pool.type} ({pool.path})</p>
                    </div>
                  </div>
                  <Badge variant={pool.active ? "default" : "secondary"}>
                    {pool.active ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-slate-600">
                    <span>Used Space</span>
                    <span>{pool.usedGB} GB / {pool.capacityGB} GB</span>
                  </div>
                  <Progress 
                    value={(pool.usedGB / pool.capacityGB) * 100} 
                    className="h-2"
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Storage Volumes */}
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-medium text-slate-900">Storage Volumes</h3>
        </div>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Size</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Format</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {volumes?.map((volume) => (
                  <tr key={volume.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">{volume.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">{volume.sizeGB} GB</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">{volume.format}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
