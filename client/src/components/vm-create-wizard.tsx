import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { insertVirtualMachineSchema } from "@shared/schema";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";

interface VmCreateWizardProps {
  onComplete: () => void;
}

const wizardSchema = insertVirtualMachineSchema.extend({
  installSource: z.enum(["local-iso", "network-install", "pxe-boot", "import-disk"]),
  isoPath: z.string().optional(),
});

type WizardFormData = z.infer<typeof wizardSchema>;

export function VmCreateWizard({ onComplete }: VmCreateWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<WizardFormData>({
    resolver: zodResolver(wizardSchema),
    defaultValues: {
      name: "",
      os: "",
      status: "stopped",
      cpuCores: 2,
      memoryMB: 2048,
      diskGB: 20,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      installSource: "local-iso",
      isoPath: "",
    },
  });

  const createVmMutation = useMutation({
    mutationFn: async (data: WizardFormData) => {
      const { installSource, isoPath, ...vmData } = data;
      const response = await apiRequest("POST", "/api/vms", vmData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/vms"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      toast({ title: "Virtual machine created successfully" });
      onComplete();
    },
    onError: () => {
      toast({ title: "Failed to create virtual machine", variant: "destructive" });
    },
  });

  const onSubmit = (data: WizardFormData) => {
    createVmMutation.mutate(data);
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const stepTitles = [
    "Installation Source",
    "OS & Version",
    "Hardware",
    "Summary",
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-medium text-slate-900">Create New Virtual Machine</h3>
          <p className="text-sm text-slate-500 mt-1">Follow the steps below to create a new virtual machine</p>
        </div>
        
        {/* Wizard Steps */}
        <div className="px-6 py-4 border-b border-slate-200">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              {stepTitles.map((title, index) => {
                const stepNumber = index + 1;
                const isActive = currentStep === stepNumber;
                const isCompleted = currentStep > stepNumber;
                
                return (
                  <li key={stepNumber} className={`relative ${index < stepTitles.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>
                    {index < stepTitles.length - 1 && (
                      <div className="absolute inset-0 flex items-center" aria-hidden="true">
                        <div className={`h-0.5 w-full ${isCompleted ? 'bg-blue-600' : 'bg-slate-200'}`}></div>
                      </div>
                    )}
                    <button
                      onClick={() => setCurrentStep(stepNumber)}
                      className={`relative w-8 h-8 flex items-center justify-center rounded-full hover:opacity-80 ${
                        isActive || isCompleted
                          ? 'bg-blue-600 text-white'
                          : 'bg-white border-2 border-slate-300 text-slate-500'
                      }`}
                    >
                      <span className="font-medium text-sm">{stepNumber}</span>
                    </button>
                  </li>
                );
              })}
            </ol>
          </nav>
          <div className="flex justify-between text-sm text-slate-600 mt-2">
            {stepTitles.map((title) => (
              <span key={title}>{title}</span>
            ))}
          </div>
        </div>

        <CardContent className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              {/* Step 1: Installation Source */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <h4 className="text-lg font-medium text-slate-900">Choose Installation Source</h4>
                  
                  <FormField
                    control={form.control}
                    name="installSource"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="space-y-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="local-iso" id="local-iso" />
                              <Label htmlFor="local-iso">Local install media (ISO image or CDROM)</Label>
                            </div>
                            
                            {field.value === "local-iso" && (
                              <div className="ml-6">
                                <FormField
                                  control={form.control}
                                  name="isoPath"
                                  render={({ field: isoField }) => (
                                    <FormItem>
                                      <div className="flex items-center space-x-4">
                                        <FormControl>
                                          <Input
                                            placeholder="/path/to/image.iso"
                                            {...isoField}
                                            className="flex-1"
                                          />
                                        </FormControl>
                                        <Button type="button" variant="outline">
                                          Browse
                                        </Button>
                                      </div>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="network-install" id="network-install" />
                              <Label htmlFor="network-install">Network Install (HTTP, HTTPS, FTP)</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="pxe-boot" id="pxe-boot" />
                              <Label htmlFor="pxe-boot">Network Boot (PXE)</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="import-disk" id="import-disk" />
                              <Label htmlFor="import-disk">Import existing disk image</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Step 2: OS & Version */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <h4 className="text-lg font-medium text-slate-900">Operating System & Version</h4>
                  
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Virtual Machine Name</FormLabel>
                        <FormControl>
                          <Input placeholder="my-vm" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="os"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Operating System</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select an operating system" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Ubuntu 22.04 LTS">Ubuntu 22.04 LTS</SelectItem>
                            <SelectItem value="Ubuntu 20.04 LTS">Ubuntu 20.04 LTS</SelectItem>
                            <SelectItem value="CentOS 9 Stream">CentOS 9 Stream</SelectItem>
                            <SelectItem value="Rocky Linux 9">Rocky Linux 9</SelectItem>
                            <SelectItem value="Debian 11">Debian 11</SelectItem>
                            <SelectItem value="Windows 11 Pro">Windows 11 Pro</SelectItem>
                            <SelectItem value="Windows 10 Pro">Windows 10 Pro</SelectItem>
                            <SelectItem value="Windows Server 2022">Windows Server 2022</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Step 3: Hardware */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <h4 className="text-lg font-medium text-slate-900">Hardware Configuration</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="cpuCores"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CPU Cores</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              max="32"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="memoryMB"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Memory (MB)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="512"
                              max="65536"
                              step="512"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="diskGB"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Disk Size (GB)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="10"
                              max="1000"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}

              {/* Step 4: Summary */}
              {currentStep === 4 && (
                <div className="space-y-6">
                  <h4 className="text-lg font-medium text-slate-900">Summary</h4>
                  
                  <div className="bg-slate-50 rounded-lg p-4 space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-slate-700">Name:</span>
                        <span className="ml-2 text-slate-900">{form.watch("name")}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">OS:</span>
                        <span className="ml-2 text-slate-900">{form.watch("os")}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">CPU Cores:</span>
                        <span className="ml-2 text-slate-900">{form.watch("cpuCores")}</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">Memory:</span>
                        <span className="ml-2 text-slate-900">{(form.watch("memoryMB") / 1024).toFixed(1)} GB</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">Disk Size:</span>
                        <span className="ml-2 text-slate-900">{form.watch("diskGB")} GB</span>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">Install Source:</span>
                        <span className="ml-2 text-slate-900">{form.watch("installSource")}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation */}
              <div className="mt-8 flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                >
                  Previous
                </Button>
                
                {currentStep < 4 ? (
                  <Button type="button" onClick={nextStep}>
                    Next
                  </Button>
                ) : (
                  <Button type="submit" disabled={createVmMutation.isPending}>
                    {createVmMutation.isPending ? "Creating..." : "Create VM"}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
