import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequestJson } from "@/lib/queryClient";
import { 
  User, 
  Key, 
  Shield, 
  Plus, 
  Edit, 
  Trash2, 
  Download,
  Upload,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  Clock,
  Settings
} from "lucide-react";
import type { User as UserType, SshKey } from "@shared/schema";

interface UserProfileProps {
  user: UserType;
  onLogout: () => void;
}

export function UserProfile({ user, onLogout }: UserProfileProps) {
  const [sshKeyDialogOpen, setSshKeyDialogOpen] = useState(false);
  const [showPrivateKey, setShowPrivateKey] = useState<{ [key: string]: boolean }>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // SSH Key form state
  const [sshKeyForm, setSshKeyForm] = useState({
    name: "",
    publicKey: "",
    privateKey: "",
    keyType: "rsa",
  });

  // Fetch user's SSH keys
  const { data: sshKeys, isLoading: keysLoading } = useQuery<SshKey[]>({
    queryKey: ["/api/auth/ssh-keys"],
  });

  // Create SSH key mutation
  const createSshKeyMutation = useMutation({
    mutationFn: (keyData: any) => apiRequestJson("/api/auth/ssh-keys", "POST", keyData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/auth/ssh-keys"] });
      setSshKeyDialogOpen(false);
      setSshKeyForm({ name: "", publicKey: "", privateKey: "", keyType: "rsa" });
      toast({
        title: "SSH Key Added",
        description: "SSH key has been successfully saved to your profile",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add SSH key",
        variant: "destructive",
      });
    },
  });

  // Delete SSH key mutation
  const deleteSshKeyMutation = useMutation({
    mutationFn: (keyId: string) => apiRequestJson(`/api/auth/ssh-keys/${keyId}`, "DELETE"),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/auth/ssh-keys"] });
      toast({
        title: "SSH Key Deleted",
        description: "SSH key has been removed from your profile",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete SSH key",
        variant: "destructive",
      });
    },
  });

  const handleSshKeySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!sshKeyForm.name || !sshKeyForm.publicKey) {
      toast({
        title: "Validation Error",
        description: "Name and public key are required",
        variant: "destructive",
      });
      return;
    }

    createSshKeyMutation.mutate(sshKeyForm);
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied",
        description: `${type} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const togglePrivateKeyVisibility = (keyId: string) => {
    setShowPrivateKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const formatKeyType = (keyType: string) => {
    return keyType.toUpperCase();
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">User Profile</h1>
          <p className="text-slate-600 mt-1">Manage your account settings and SSH keys</p>
        </div>
        <Button variant="outline" onClick={onLogout}>
          Sign Out
        </Button>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="ssh-keys">SSH Keys</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Username</Label>
                  <Input value={user.username} disabled />
                </div>
                <div>
                  <Label>Email</Label>
                  <Input value={user.email} disabled />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>First Name</Label>
                  <Input value={user.firstName || ""} disabled />
                </div>
                <div>
                  <Label>Last Name</Label>
                  <Input value={user.lastName || ""} disabled />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Label>Account Status</Label>
                <Badge variant={user.isActive ? "default" : "destructive"}>
                  {user.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-slate-400" />
                <span className="text-sm text-slate-600">
                  Member since {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : "N/A"}
                </span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* SSH Keys Tab */}
        <TabsContent value="ssh-keys">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Key className="h-5 w-5 mr-2" />
                  SSH Keys
                </CardTitle>
                <Dialog open={sshKeyDialogOpen} onOpenChange={setSshKeyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-1" />
                      Add SSH Key
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Add SSH Key</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSshKeySubmit} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="keyName">Key Name</Label>
                          <Input
                            id="keyName"
                            placeholder="My SSH Key"
                            value={sshKeyForm.name}
                            onChange={(e) => setSshKeyForm(prev => ({ ...prev, name: e.target.value }))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="keyType">Key Type</Label>
                          <Select 
                            value={sshKeyForm.keyType} 
                            onValueChange={(value) => setSshKeyForm(prev => ({ ...prev, keyType: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="rsa">RSA</SelectItem>
                              <SelectItem value="ecdsa">ECDSA</SelectItem>
                              <SelectItem value="ed25519">Ed25519</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="publicKey">Public Key</Label>
                        <Textarea
                          id="publicKey"
                          placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAB..."
                          value={sshKeyForm.publicKey}
                          onChange={(e) => setSshKeyForm(prev => ({ ...prev, publicKey: e.target.value }))}
                          rows={4}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="privateKey">Private Key (Optional)</Label>
                        <Textarea
                          id="privateKey"
                          placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
                          value={sshKeyForm.privateKey}
                          onChange={(e) => setSshKeyForm(prev => ({ ...prev, privateKey: e.target.value }))}
                          rows={6}
                        />
                        <p className="text-xs text-slate-500 mt-1">
                          Private key will be encrypted and stored securely
                        </p>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => setSshKeyDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={createSshKeyMutation.isPending}>
                          {createSshKeyMutation.isPending ? "Adding..." : "Add Key"}
                        </Button>
                      </div>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {keysLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="text-slate-600 mt-2">Loading SSH keys...</p>
                </div>
              ) : sshKeys && sshKeys.length > 0 ? (
                <div className="space-y-4">
                  {sshKeys.map((key) => (
                    <Card key={key.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium">{key.name}</h4>
                            <Badge variant="secondary">{formatKeyType(key.keyType)}</Badge>
                            {key.isDefault && (
                              <Badge variant="default">Default</Badge>
                            )}
                          </div>
                          
                          <div className="space-y-2">
                            <div>
                              <Label className="text-xs text-slate-600">Public Key</Label>
                              <div className="flex items-center space-x-2">
                                <code className="text-xs bg-slate-100 p-2 rounded flex-1 truncate">
                                  {key.publicKey}
                                </code>
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => copyToClipboard(key.publicKey, "Public key")}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            
                            {key.privateKey && (
                              <div>
                                <Label className="text-xs text-slate-600">Private Key</Label>
                                <div className="flex items-center space-x-2">
                                  <code className="text-xs bg-slate-100 p-2 rounded flex-1 truncate">
                                    {showPrivateKey[key.id] ? key.privateKey : "••••••••••••••••••••"}
                                  </code>
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => togglePrivateKeyVisibility(key.id)}
                                  >
                                    {showPrivateKey[key.id] ? (
                                      <EyeOff className="h-3 w-3" />
                                    ) : (
                                      <Eye className="h-3 w-3" />
                                    )}
                                  </Button>
                                  {showPrivateKey[key.id] && (
                                    <Button 
                                      size="sm" 
                                      variant="ghost"
                                      onClick={() => copyToClipboard(key.privateKey || "", "Private key")}
                                    >
                                      <Copy className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-4 text-xs text-slate-500">
                              <span>Fingerprint: {key.fingerprint}</span>
                              <span>Created: {new Date(key.createdAt!).toLocaleDateString()}</span>
                              {key.lastUsed && (
                                <span>Last used: {new Date(key.lastUsed).toLocaleDateString()}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex space-x-1 ml-4">
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => deleteSshKeyMutation.mutate(key.id)}
                            disabled={deleteSshKeyMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Key className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">No SSH Keys</h3>
                  <p className="text-slate-600 mb-4">
                    Add SSH keys to securely connect to hypervisors
                  </p>
                  <Button onClick={() => setSshKeyDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Your First SSH Key
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Roles & Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">Role Management</h3>
                <p className="text-slate-600">
                  Contact your administrator to view or modify your roles and permissions
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}