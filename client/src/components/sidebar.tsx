import { cn } from "@/lib/utils";
import { Server, LayoutDashboard, HardDrive, Wifi, Plus, Monitor } from "lucide-react";
import type { TabType } from "@/pages/dashboard";

interface SidebarProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const navigation = [
  { id: "overview" as TabType, name: "Overview", icon: LayoutDashboard },
  { id: "hypervisors" as TabType, name: "Hypervisors", icon: Server },
  { id: "machines" as TabType, name: "Virtual Machines", icon: Monitor },
  { id: "storage" as TabType, name: "Storage Pools", icon: HardDrive },
  { id: "networks" as TabType, name: "Networks", icon: Wifi },
  { id: "create" as TabType, name: "Create VM", icon: Plus },
];

export function Sidebar({ activeTab, onTabChange }: SidebarProps) {
  return (
    <div className="hidden md:flex md:flex-shrink-0">
      <div className="flex flex-col w-64">
        <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-slate-200">
          {/* Logo and title */}
          <div className="flex items-center flex-shrink-0 px-4">
            <Server className="h-8 w-8 text-blue-600 mr-3" />
            <h1 className="text-xl font-semibold text-slate-800">VM Manager</h1>
          </div>
          
          {/* Navigation */}
          <nav className="mt-8 flex-1 px-4 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    "w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                    isActive
                      ? "bg-blue-50 text-blue-700"
                      : "text-slate-600 hover:bg-slate-50 hover:text-slate-900"
                  )}
                >
                  <Icon
                    className={cn(
                      "mr-3 h-5 w-5",
                      isActive ? "text-blue-500" : "text-slate-400"
                    )}
                  />
                  {item.name}
                </button>
              );
            })}
          </nav>
          
          {/* Connection Status */}
          <div className="flex-shrink-0 px-4 py-4 border-t border-slate-200">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-slate-600">Connected to localhost</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
