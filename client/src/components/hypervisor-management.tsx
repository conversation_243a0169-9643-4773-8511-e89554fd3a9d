import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { HypervisorAddDialog } from "./hypervisor-add-dialog";
import { 
  Server, 
  Search, 
  Filter, 
  Plus, 
  Settings, 
  Activity,
  MapPin,
  Cpu,
  HardDrive,
  Link,
  Wifi,
  WifiOff,
  ChevronDown,
  ChevronRight,
  Monitor,
  MemoryStick,
  Clock
} from "lucide-react";
import type { Hypervisor } from "@shared/schema";

export function HypervisorManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [locationFilter, setLocationFilter] = useState<string>("all");
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: hypervisors, isLoading } = useQuery<Hypervisor[]>({
    queryKey: ["/api/hypervisors"],
  });

  const testConnectionMutation = useMutation({
    mutationFn: (hypervisorId: string) => 
      apiRequest(`/api/hypervisors/${hypervisorId}/test-connection`, "POST"),
    onSuccess: (data: any, hypervisorId: string) => {
      if (data.success) {
        toast({
          title: "Connection Successful",
          description: `Connected to hypervisor via ${data.connectionType}`,
        });
      } else {
        toast({
          title: "Connection Failed",
          description: data.error || "Unable to connect to hypervisor",
          variant: "destructive",
        });
      }
      queryClient.invalidateQueries({ queryKey: ["/api/hypervisors"] });
    },
    onError: () => {
      toast({
        title: "Test Failed",
        description: "Unable to test hypervisor connection",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return <div>Loading hypervisors...</div>;
  }

  const filteredHypervisors = hypervisors?.filter(hypervisor => {
    const matchesSearch = hypervisor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hypervisor.hostname.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hypervisor.ipAddress.includes(searchTerm);
    const matchesStatus = statusFilter === "all" || hypervisor.status === statusFilter;
    const matchesLocation = locationFilter === "all" || hypervisor.location === locationFilter;
    
    return matchesSearch && matchesStatus && matchesLocation;
  }) || [];

  const uniqueLocations = Array.from(new Set(hypervisors?.map(h => h.location).filter(Boolean))) || [];

  const toggleRowExpansion = (hypervisorId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(hypervisorId)) {
      newExpanded.delete(hypervisorId);
    } else {
      newExpanded.add(hypervisorId);
    }
    setExpandedRows(newExpanded);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      online: { variant: "default" as const, className: "bg-green-100 text-green-800 border-green-200" },
      offline: { variant: "destructive" as const, className: "bg-red-100 text-red-800 border-red-200" },
      maintenance: { variant: "secondary" as const, className: "bg-amber-100 text-amber-800 border-amber-200" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.offline;
    
    return (
      <Badge variant={config.variant} className={config.className}>
        <div className={`w-1.5 h-1.5 rounded-full mr-1 ${
          status === "online" ? "bg-green-500" :
          status === "offline" ? "bg-red-500" : "bg-amber-500"
        }`}></div>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search hypervisors by name, hostname, or IP..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="online">Online</option>
                <option value="offline">Offline</option>
                <option value="maintenance">Maintenance</option>
              </select>
              
              <select
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
                className="px-3 py-2 border border-slate-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Locations</option>
                {uniqueLocations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
              
              <HypervisorAddDialog>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Host
                </Button>
              </HypervisorAddDialog>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Server className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-600">Total Hypervisors</p>
                <p className="text-2xl font-bold text-slate-900">{filteredHypervisors.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Wifi className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-600">Online</p>
                <p className="text-2xl font-bold text-green-700">
                  {filteredHypervisors.filter(h => h.status === "online").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Monitor className="h-8 w-8 text-purple-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-600">Total VMs</p>
                <p className="text-2xl font-bold text-slate-900">
                  {filteredHypervisors.reduce((sum, h) => sum + h.totalVMs, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Cpu className="h-8 w-8 text-orange-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-slate-600">Avg CPU Usage</p>
                <p className="text-2xl font-bold text-slate-900">
                  {filteredHypervisors.length > 0 
                    ? Math.round(filteredHypervisors.reduce((sum, h) => sum + h.cpuUsage, 0) / filteredHypervisors.length)
                    : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Hypervisor Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12"></TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>VMs</TableHead>
                <TableHead>CPU Usage</TableHead>
                <TableHead>Memory Usage</TableHead>
                <TableHead>Connection</TableHead>
                <TableHead className="w-32">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredHypervisors.map((hypervisor) => (
                <Collapsible key={hypervisor.id} asChild>
                  <>
                    <TableRow className="cursor-pointer hover:bg-slate-50">
                      <TableCell>
                        <CollapsibleTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleRowExpansion(hypervisor.id)}
                          >
                            {expandedRows.has(hypervisor.id) ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                        </CollapsibleTrigger>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Server className="h-4 w-4 text-blue-500 mr-2" />
                          <div>
                            <div className="font-medium">{hypervisor.name}</div>
                            <div className="text-sm text-slate-500">{hypervisor.hostname}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(hypervisor.status)}</TableCell>
                      <TableCell className="font-mono text-sm">{hypervisor.ipAddress}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 text-slate-400 mr-1" />
                          <span className="text-sm">{hypervisor.location}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Monitor className="h-3 w-3 text-slate-400" />
                          <span className="text-sm">{hypervisor.runningVMs}/{hypervisor.totalVMs}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={hypervisor.cpuUsage} className="w-16 h-2" />
                          <span className="text-sm w-8">{hypervisor.cpuUsage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={hypervisor.memoryUsage} className="w-16 h-2" />
                          <span className="text-sm w-8">{hypervisor.memoryUsage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Link className="h-3 w-3 text-slate-400 mr-1" />
                          <span className="text-xs">{hypervisor.connectionType || 'qemu+ssh'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              testConnectionMutation.mutate(hypervisor.id);
                            }}
                            disabled={testConnectionMutation.isPending}
                          >
                            {hypervisor.status === "online" ? (
                              <Wifi className="h-4 w-4 text-green-500" />
                            ) : (
                              <WifiOff className="h-4 w-4 text-red-500" />
                            )}
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                    <CollapsibleContent asChild>
                      <TableRow>
                        <TableCell colSpan={10} className="p-0">
                          <div className="bg-slate-50 p-6 border-t">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              {/* System Information */}
                              <Card>
                                <CardContent className="p-4">
                                  <h4 className="font-medium text-slate-900 mb-3 flex items-center">
                                    <Server className="h-4 w-4 mr-2" />
                                    System Information
                                  </h4>
                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">Hypervisor Type:</span>
                                      <span className="font-medium">{hypervisor.hypervisorType.toUpperCase()}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">Version:</span>
                                      <span className="font-medium">{hypervisor.version || "N/A"}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">SSH Port:</span>
                                      <span className="font-medium">{hypervisor.sshPort || 22}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">SSH User:</span>
                                      <span className="font-medium">{hypervisor.sshUsername || "root"}</span>
                                    </div>
                                    {hypervisor.ociInstanceId && (
                                      <div className="flex justify-between">
                                        <span className="text-slate-600">OCI Instance:</span>
                                        <span className="font-medium text-xs">{hypervisor.ociInstanceId.slice(-8)}</span>
                                      </div>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>

                              {/* Resource Details */}
                              <Card>
                                <CardContent className="p-4">
                                  <h4 className="font-medium text-slate-900 mb-3 flex items-center">
                                    <Cpu className="h-4 w-4 mr-2" />
                                    Resource Details
                                  </h4>
                                  <div className="space-y-3">
                                    <div>
                                      <div className="flex justify-between text-sm mb-1">
                                        <span className="text-slate-600">CPU ({hypervisor.cpuCores} cores)</span>
                                        <span className="font-medium">{hypervisor.cpuUsage}%</span>
                                      </div>
                                      <Progress value={hypervisor.cpuUsage} className="h-2" />
                                    </div>
                                    <div>
                                      <div className="flex justify-between text-sm mb-1">
                                        <span className="text-slate-600">Memory ({hypervisor.memoryGB} GB)</span>
                                        <span className="font-medium">{hypervisor.memoryUsage}%</span>
                                      </div>
                                      <Progress value={hypervisor.memoryUsage} className="h-2" />
                                    </div>
                                    <div className="pt-2 border-t">
                                      <div className="flex justify-between text-sm">
                                        <span className="text-slate-600">Running VMs:</span>
                                        <span className="font-medium">{hypervisor.runningVMs}</span>
                                      </div>
                                      <div className="flex justify-between text-sm">
                                        <span className="text-slate-600">Total VMs:</span>
                                        <span className="font-medium">{hypervisor.totalVMs}</span>
                                      </div>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>

                              {/* Status & Activity */}
                              <Card>
                                <CardContent className="p-4">
                                  <h4 className="font-medium text-slate-900 mb-3 flex items-center">
                                    <Clock className="h-4 w-4 mr-2" />
                                    Status & Activity
                                  </h4>
                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">Last Seen:</span>
                                      <span className="font-medium">
                                        {hypervisor.lastSeen ? new Date(hypervisor.lastSeen).toLocaleString() : "Never"}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-slate-600">Created:</span>
                                      <span className="font-medium">
                                        {hypervisor.createdAt ? new Date(hypervisor.createdAt).toLocaleDateString() : "N/A"}
                                      </span>
                                    </div>
                                    <div className="pt-3 border-t">
                                      <div className="flex space-x-2">
                                        <Button size="sm" variant="outline">
                                          <Activity className="h-4 w-4 mr-1" />
                                          View Logs
                                        </Button>
                                        <Button size="sm" variant="outline">
                                          <Settings className="h-4 w-4 mr-1" />
                                          Configure
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    </CollapsibleContent>
                  </>
                </Collapsible>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {filteredHypervisors.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Server className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No hypervisors found</h3>
            <p className="text-slate-500">
              {searchTerm || statusFilter !== "all" || locationFilter !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Add your first hypervisor to get started."}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}