import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Monitor, Pause, Square, Cpu, Plus, Download, FolderPlus, Server, HardDrive } from "lucide-react";
import { useVMs } from "@/hooks/use-vms";
import type { Activity } from "@shared/schema";

interface OverviewStatsProps {
  onCreateVM: () => void;
}

interface DashboardStats {
  hypervisors: {
    total: number;
    online: number;
    offline: number;
    maintenance: number;
  };
  vms: {
    total: number;
    running: number;
    paused: number;
    stopped: number;
  };
  resources: {
    totalCpuCores: number;
    avgCpuUsage: number;
    totalMemoryGB: number;
    avgMemoryUsage: number;
  };
}

export function OverviewStats({ onCreateVM }: OverviewStatsProps) {
  const { data: stats, isLoading: statsLoading } = useQuery<DashboardStats>({
    queryKey: ["/api/stats"],
  });

  const { data: activities, isLoading: activitiesLoading } = useQuery<Activity[]>({
    queryKey: ["/api/activities"],
  });

  if (statsLoading) {
    return <div>Loading stats...</div>;
  }

  return (
    <div className="space-y-8">
      {/* Infrastructure Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Server className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-slate-500 truncate">Total Hypervisors</dt>
                  <dd className="text-lg font-medium text-slate-900">{stats?.hypervisors.total || 0}</dd>
                  <dd className="text-xs text-green-600">{stats?.hypervisors.online || 0} online</dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Monitor className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-slate-500 truncate">Total VMs</dt>
                  <dd className="text-lg font-medium text-slate-900">{stats?.vms.total || 0}</dd>
                  <dd className="text-xs text-green-600">{stats?.vms.running || 0} running</dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Cpu className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-slate-500 truncate">CPU Resources</dt>
                  <dd className="text-lg font-medium text-slate-900">{stats?.resources.totalCpuCores || 0} cores</dd>
                  <dd className="text-xs text-slate-600">{stats?.resources.avgCpuUsage || 0}% avg usage</dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <HardDrive className="h-8 w-8 text-indigo-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-slate-500 truncate">Memory Resources</dt>
                  <dd className="text-lg font-medium text-slate-900">{stats?.resources.totalMemoryGB || 0} GB</dd>
                  <dd className="text-xs text-slate-600">{stats?.resources.avgMemoryUsage || 0}% avg usage</dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Status Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <div className="px-6 py-4 border-b border-slate-200">
            <h3 className="text-lg font-medium text-slate-900">Hypervisor Status</h3>
          </div>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Online</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.hypervisors.online || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Offline</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.hypervisors.offline || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-amber-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Maintenance</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.hypervisors.maintenance || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <div className="px-6 py-4 border-b border-slate-200">
            <h3 className="text-lg font-medium text-slate-900">VM Distribution</h3>
          </div>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Running</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.vms.running || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-amber-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Paused</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.vms.paused || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                  <span className="text-sm font-medium text-slate-700">Stopped</span>
                </div>
                <span className="text-sm font-medium text-slate-900">{stats?.vms.stopped || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-medium text-slate-900">Quick Actions</h3>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2 text-left"
              onClick={onCreateVM}
            >
              <Plus className="h-6 w-6 text-blue-500" />
              <div>
                <div className="font-medium text-slate-900">Create New VM</div>
                <div className="text-sm text-slate-500">Launch the VM creation wizard</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2 text-left"
            >
              <Download className="h-6 w-6 text-green-500" />
              <div>
                <div className="font-medium text-slate-900">Import VM</div>
                <div className="text-sm text-slate-500">Import existing virtual machine</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2 text-left"
            >
              <FolderPlus className="h-6 w-6 text-purple-500" />
              <div>
                <div className="font-medium text-slate-900">Add Storage Pool</div>
                <div className="text-sm text-slate-500">Configure new storage location</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <div className="px-6 py-4 border-b border-slate-200">
          <h3 className="text-lg font-medium text-slate-900">Recent Activity</h3>
        </div>
        <CardContent className="p-6">
          {activitiesLoading ? (
            <div>Loading activities...</div>
          ) : (
            <div className="space-y-4">
              {activities?.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.type === "success" ? "bg-green-500" :
                    activity.type === "warning" ? "bg-amber-500" :
                    activity.type === "error" ? "bg-red-500" : "bg-blue-500"
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-slate-900">{activity.message}</p>
                    <p className="text-xs text-slate-500">
                      {activity.timestamp ? new Date(activity.timestamp).toLocaleString() : ""}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
