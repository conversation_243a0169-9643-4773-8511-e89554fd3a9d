import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Plus, Cloud, Server, Key, Globe, Shield } from "lucide-react";
import type { InsertHypervisor } from "@shared/schema";

interface HypervisorAddDialogProps {
  children?: React.ReactNode;
}

export function HypervisorAddDialog({ children }: HypervisorAddDialogProps) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("manual");
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredHosts, setDiscoveredHosts] = useState<any[]>([]);
  
  // Manual form state
  const [manualForm, setManualForm] = useState({
    name: "",
    hostname: "",
    ipAddress: "",
    connectionType: "qemu+ssh",
    sshPort: "22",
    username: "root",
    keyFile: "",
    location: "",
    cpuCores: "",
    memoryGB: "",
  });

  // OCI form state
  const [ociForm, setOciForm] = useState({
    region: "",
    compartmentId: "",
    tenancyId: "",
    userId: "",
    keyFile: "",
    fingerprint: "",
    autoImport: true,
    location: "",
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const addHypervisorMutation = useMutation({
    mutationFn: (data: InsertHypervisor) => apiRequest("/api/hypervisors", "POST", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/hypervisors"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
      toast({
        title: "Success",
        description: "Hypervisor added successfully",
      });
      setOpen(false);
      resetForms();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add hypervisor",
        variant: "destructive",
      });
    },
  });

  const scanOciMutation = useMutation({
    mutationFn: (ociConfig: any) => apiRequest("/api/hypervisors/scan-oci", "POST", ociConfig),
    onSuccess: (data: any) => {
      setDiscoveredHosts(data.hosts || []);
      toast({
        title: "Scan Complete",
        description: `Found ${data.hosts?.length || 0} compute instances`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Scan Failed",
        description: error.message || "Failed to scan OCI instances",
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsScanning(false);
    },
  });

  const resetForms = () => {
    setManualForm({
      name: "",
      hostname: "",
      ipAddress: "",
      connectionType: "qemu+ssh",
      sshPort: "22",
      username: "root",
      keyFile: "",
      location: "",
      cpuCores: "",
      memoryGB: "",
    });
    setOciForm({
      region: "",
      compartmentId: "",
      tenancyId: "",
      userId: "",
      keyFile: "",
      fingerprint: "",
      autoImport: true,
      location: "",
    });
    setDiscoveredHosts([]);
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const hypervisorData: InsertHypervisor = {
      name: manualForm.name,
      hostname: manualForm.hostname,
      ipAddress: manualForm.ipAddress,
      status: "offline",
      hypervisorType: "kvm",
      cpuCores: parseInt(manualForm.cpuCores) || 0,
      cpuUsage: 0,
      memoryGB: parseInt(manualForm.memoryGB) || 0,
      memoryUsage: 0,
      totalVMs: 0,
      runningVMs: 0,
      location: manualForm.location,
      connectionType: manualForm.connectionType,
      sshPort: parseInt(manualForm.sshPort),
      sshUsername: manualForm.username,
      sshKeyFile: manualForm.keyFile,
    };

    addHypervisorMutation.mutate(hypervisorData);
  };

  const handleOciScan = () => {
    setIsScanning(true);
    scanOciMutation.mutate(ociForm);
  };

  const handleImportOciHost = (host: any) => {
    const hypervisorData: InsertHypervisor = {
      name: host.displayName,
      hostname: host.displayName,
      ipAddress: host.publicIp || host.privateIp,
      status: host.lifecycleState === "RUNNING" ? "online" : "offline",
      hypervisorType: "kvm",
      cpuCores: host.shape.ocpus || 0,
      cpuUsage: 0,
      memoryGB: host.shape.memoryInGBs || 0,
      memoryUsage: 0,
      totalVMs: 0,
      runningVMs: 0,
      location: `OCI-${host.availabilityDomain}`,
      ociInstanceId: host.id,
      ociCompartmentId: host.compartmentId,
      connectionType: "qemu+ssh",
      sshPort: 22,
      sshUsername: "opc",
    };

    addHypervisorMutation.mutate(hypervisorData);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-1" />
            Add Hypervisor
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Server className="h-5 w-5 mr-2" />
            Add Hypervisor Host
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="manual" className="flex items-center">
              <Server className="h-4 w-4 mr-1" />
              Manual
            </TabsTrigger>
            <TabsTrigger value="oci" className="flex items-center">
              <Cloud className="h-4 w-4 mr-1" />
              Oracle Cloud
            </TabsTrigger>
          </TabsList>

          <TabsContent value="manual" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Manual Hypervisor Configuration</CardTitle>
                <CardDescription>
                  Add a KVM hypervisor with qemu+ssh connection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleManualSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Display Name</Label>
                      <Input
                        id="name"
                        value={manualForm.name}
                        onChange={(e) => setManualForm(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="kvm-host-001"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="hostname">Hostname</Label>
                      <Input
                        id="hostname"
                        value={manualForm.hostname}
                        onChange={(e) => setManualForm(prev => ({ ...prev, hostname: e.target.value }))}
                        placeholder="host.datacenter.local"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="ipAddress">IP Address</Label>
                      <Input
                        id="ipAddress"
                        value={manualForm.ipAddress}
                        onChange={(e) => setManualForm(prev => ({ ...prev, ipAddress: e.target.value }))}
                        placeholder="*************"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={manualForm.location}
                        onChange={(e) => setManualForm(prev => ({ ...prev, location: e.target.value }))}
                        placeholder="DC1-Rack01"
                      />
                    </div>
                  </div>

                  <div className="space-y-4 p-4 bg-slate-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Key className="h-4 w-4 text-slate-600" />
                      <h4 className="font-medium">SSH Connection Settings</h4>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="connectionType">Connection Type</Label>
                        <Select 
                          value={manualForm.connectionType} 
                          onValueChange={(value) => setManualForm(prev => ({ ...prev, connectionType: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="qemu+ssh">qemu+ssh</SelectItem>
                            <SelectItem value="qemu+tcp">qemu+tcp</SelectItem>
                            <SelectItem value="qemu+tls">qemu+tls</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="username">SSH Username</Label>
                        <Input
                          id="username"
                          value={manualForm.username}
                          onChange={(e) => setManualForm(prev => ({ ...prev, username: e.target.value }))}
                          placeholder="root"
                        />
                      </div>
                      <div>
                        <Label htmlFor="sshPort">SSH Port</Label>
                        <Input
                          id="sshPort"
                          type="number"
                          value={manualForm.sshPort}
                          onChange={(e) => setManualForm(prev => ({ ...prev, sshPort: e.target.value }))}
                          placeholder="22"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="keyFile">SSH Key File Path</Label>
                      <Input
                        id="keyFile"
                        value={manualForm.keyFile}
                        onChange={(e) => setManualForm(prev => ({ ...prev, keyFile: e.target.value }))}
                        placeholder="/path/to/ssh/key"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="cpuCores">CPU Cores</Label>
                      <Input
                        id="cpuCores"
                        type="number"
                        value={manualForm.cpuCores}
                        onChange={(e) => setManualForm(prev => ({ ...prev, cpuCores: e.target.value }))}
                        placeholder="8"
                      />
                    </div>
                    <div>
                      <Label htmlFor="memoryGB">Memory (GB)</Label>
                      <Input
                        id="memoryGB"
                        type="number"
                        value={manualForm.memoryGB}
                        onChange={(e) => setManualForm(prev => ({ ...prev, memoryGB: e.target.value }))}
                        placeholder="32"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={addHypervisorMutation.isPending}>
                      {addHypervisorMutation.isPending ? "Adding..." : "Add Hypervisor"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="oci" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Oracle Cloud Infrastructure</CardTitle>
                <CardDescription>
                  Automatically discover and import compute instances from OCI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-orange-600" />
                    <h4 className="font-medium text-orange-800">OCI API Configuration</h4>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="region">Region</Label>
                      <Select 
                        value={ociForm.region} 
                        onValueChange={(value) => setOciForm(prev => ({ ...prev, region: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="us-ashburn-1">US East (Ashburn)</SelectItem>
                          <SelectItem value="us-phoenix-1">US West (Phoenix)</SelectItem>
                          <SelectItem value="eu-frankfurt-1">EU (Frankfurt)</SelectItem>
                          <SelectItem value="ap-tokyo-1">Asia (Tokyo)</SelectItem>
                          <SelectItem value="uk-london-1">UK South (London)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="compartmentId">Compartment OCID</Label>
                      <Input
                        id="compartmentId"
                        value={ociForm.compartmentId}
                        onChange={(e) => setOciForm(prev => ({ ...prev, compartmentId: e.target.value }))}
                        placeholder="ocid1.compartment.oc1...."
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="tenancyId">Tenancy OCID</Label>
                      <Input
                        id="tenancyId"
                        value={ociForm.tenancyId}
                        onChange={(e) => setOciForm(prev => ({ ...prev, tenancyId: e.target.value }))}
                        placeholder="ocid1.tenancy.oc1...."
                      />
                    </div>
                    <div>
                      <Label htmlFor="userId">User OCID</Label>
                      <Input
                        id="userId"
                        value={ociForm.userId}
                        onChange={(e) => setOciForm(prev => ({ ...prev, userId: e.target.value }))}
                        placeholder="ocid1.user.oc1...."
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="ociKeyFile">API Key File Path</Label>
                      <Input
                        id="ociKeyFile"
                        value={ociForm.keyFile}
                        onChange={(e) => setOciForm(prev => ({ ...prev, keyFile: e.target.value }))}
                        placeholder="/path/to/oci_api_key.pem"
                      />
                    </div>
                    <div>
                      <Label htmlFor="fingerprint">Key Fingerprint</Label>
                      <Input
                        id="fingerprint"
                        value={ociForm.fingerprint}
                        onChange={(e) => setOciForm(prev => ({ ...prev, fingerprint: e.target.value }))}
                        placeholder="a1:b2:c3:..."
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="ociLocation">Default Location Tag</Label>
                    <Input
                      id="ociLocation"
                      value={ociForm.location}
                      onChange={(e) => setOciForm(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="OCI-Phoenix"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="autoImport"
                      checked={ociForm.autoImport}
                      onCheckedChange={(checked) => setOciForm(prev => ({ ...prev, autoImport: !!checked }))}
                    />
                    <Label htmlFor="autoImport">Automatically import discovered instances</Label>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleOciScan}
                    disabled={isScanning || !ociForm.region || !ociForm.compartmentId}
                  >
                    <Globe className="h-4 w-4 mr-1" />
                    {isScanning ? "Scanning..." : "Scan Instances"}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                    Close
                  </Button>
                </div>

                {discoveredHosts.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-medium">Discovered Instances ({discoveredHosts.length})</h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {discoveredHosts.map((host, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h5 className="font-medium">{host.displayName}</h5>
                                <Badge variant={host.lifecycleState === "RUNNING" ? "default" : "secondary"}>
                                  {host.lifecycleState}
                                </Badge>
                              </div>
                              <div className="text-sm text-slate-600 mt-1">
                                <span>{host.shape.shape}</span> • 
                                <span>{host.shape.ocpus} CPU</span> • 
                                <span>{host.shape.memoryInGBs} GB RAM</span>
                              </div>
                              <div className="text-xs text-slate-500">
                                IP: {host.publicIp || host.privateIp} • AD: {host.availabilityDomain}
                              </div>
                            </div>
                            <Button 
                              size="sm" 
                              onClick={() => handleImportOciHost(host)}
                              disabled={addHypervisorMutation.isPending}
                            >
                              Import
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}