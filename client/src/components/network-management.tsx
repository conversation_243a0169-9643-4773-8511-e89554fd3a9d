import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Wifi } from "lucide-react";
import { useNetworks } from "@/hooks/use-networks";

export function NetworkManagement() {
  const { data: networks, isLoading } = useNetworks();

  if (isLoading) {
    return <div>Loading network information...</div>;
  }

  return (
    <Card>
      <div className="px-6 py-4 border-b border-slate-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-slate-900">Virtual Networks</h3>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-1" />
            Add Network
          </Button>
        </div>
      </div>
      <CardContent className="p-0">
        <div className="divide-y divide-slate-200">
          {networks?.map((network) => (
            <div key={network.id} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Wifi className="h-6 w-6 text-blue-500 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-slate-900">{network.name}</h4>
                    <p className="text-sm text-slate-500">
                      {network.mode === "nat" ? "NAT (Network Address Translation)" :
                       network.mode === "bridge" ? "Bridge" :
                       "Isolated (Host only)"}
                    </p>
                  </div>
                </div>
                <Badge variant={network.active ? "default" : "secondary"}>
                  {network.active ? "Active" : "Inactive"}
                </Badge>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <dt className="font-medium text-slate-500">IP Range</dt>
                  <dd className="text-slate-900">{network.ipRange}</dd>
                </div>
                <div>
                  <dt className="font-medium text-slate-500">DHCP Range</dt>
                  <dd className="text-slate-900">{network.dhcpRange || "N/A"}</dd>
                </div>
                <div>
                  <dt className="font-medium text-slate-500">Bridge</dt>
                  <dd className="text-slate-900">{network.bridge || "N/A"}</dd>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
