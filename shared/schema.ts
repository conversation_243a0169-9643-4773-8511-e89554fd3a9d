import { sql } from "drizzle-orm";
import { pgTable, text, varchar, integer, timestamp, boolean, jsonb } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Hypervisor nodes (KVM hosts)
export const hypervisors = pgTable("hypervisors", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull().unique(),
  hostname: text("hostname").notNull(),
  ipAddress: text("ip_address").notNull(),
  status: text("status").notNull(), // 'online', 'offline', 'maintenance'
  hypervisorType: text("hypervisor_type").notNull().default("kvm"), // 'kvm', 'xen', 'vmware'
  version: text("version"),
  cpuCores: integer("cpu_cores").notNull(),
  cpuUsage: integer("cpu_usage").notNull().default(0), // percentage
  memoryGB: integer("memory_gb").notNull(),
  memoryUsage: integer("memory_usage").notNull().default(0), // percentage
  totalVMs: integer("total_vms").notNull().default(0),
  runningVMs: integer("running_vms").notNull().default(0),
  location: text("location"), // datacenter/rack location
  // Connection settings
  connectionType: text("connection_type").default("qemu+ssh"), // 'qemu+ssh', 'qemu+tcp', 'qemu+tls'
  sshPort: integer("ssh_port").default(22),
  sshUsername: text("ssh_username").default("root"),
  sshKeyFile: text("ssh_key_file"),
  // OCI integration fields
  ociInstanceId: text("oci_instance_id"),
  ociCompartmentId: text("oci_compartment_id"),
  ociRegion: text("oci_region"),
  lastSeen: timestamp("last_seen").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: varchar("created_by").references(() => users.id),
});

// Users and Authentication
export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: varchar("username").notNull().unique(),
  email: varchar("email").notNull().unique(),
  passwordHash: varchar("password_hash").notNull(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  isActive: boolean("is_active").notNull().default(true),
  lastLogin: timestamp("last_login"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Roles for RBAC
export const roles = pgTable("roles", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: varchar("name").notNull().unique(),
  description: text("description"),
  permissions: jsonb("permissions").notNull().default('[]'), // Array of permission strings
  createdAt: timestamp("created_at").defaultNow(),
});

// User role assignments
export const userRoles = pgTable("user_roles", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id).notNull(),
  roleId: varchar("role_id").references(() => roles.id).notNull(),
  assignedAt: timestamp("assigned_at").defaultNow(),
  assignedBy: varchar("assigned_by").references(() => users.id),
});

// SSH Keys for user profiles
export const sshKeys = pgTable("ssh_keys", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id).notNull(),
  name: varchar("name").notNull(),
  publicKey: text("public_key").notNull(),
  privateKey: text("private_key"), // Encrypted storage
  fingerprint: varchar("fingerprint").notNull(),
  keyType: varchar("key_type").notNull().default("rsa"), // rsa, ecdsa, ed25519
  isDefault: boolean("is_default").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow(),
  lastUsed: timestamp("last_used"),
});

// Session management
export const sessions = pgTable("sessions", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id).notNull(),
  token: varchar("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  ipAddress: varchar("ip_address"),
  userAgent: text("user_agent"),
});

// Audit logs for tracking user actions
export const auditLogs = pgTable("audit_logs", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").references(() => users.id),
  action: varchar("action").notNull(), // login, create_vm, delete_hypervisor, etc.
  resourceType: varchar("resource_type"), // vm, hypervisor, user, etc.
  resourceId: varchar("resource_id"),
  details: jsonb("details"),
  ipAddress: varchar("ip_address"),
  userAgent: text("user_agent"),
  timestamp: timestamp("timestamp").defaultNow(),
});

export const virtualMachines = pgTable("virtual_machines", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  hypervisorId: varchar("hypervisor_id").references(() => hypervisors.id).notNull(),
  os: text("os").notNull(),
  status: text("status").notNull(), // 'running', 'stopped', 'paused', 'suspended'
  cpuCores: integer("cpu_cores").notNull().default(1),
  cpuUsage: integer("cpu_usage").notNull().default(0), // percentage
  memoryMB: integer("memory_mb").notNull().default(1024),
  memoryUsage: integer("memory_usage").notNull().default(0), // percentage
  diskGB: integer("disk_gb").notNull().default(20),
  diskUsage: integer("disk_usage").notNull().default(0), // percentage
  vmType: text("vm_type").notNull().default("guest"), // 'guest', 'template', 'clone'
  uuid: text("uuid"), // libvirt UUID
  vnc_port: integer("vnc_port"),
  autostart: boolean("autostart").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: varchar("created_by").references(() => users.id),
});

export const storagePools = pgTable("storage_pools", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull().unique(),
  type: text("type").notNull(), // 'directory', 'lvm', 'nfs', etc.
  path: text("path").notNull(),
  capacityGB: integer("capacity_gb").notNull(),
  usedGB: integer("used_gb").notNull().default(0),
  active: boolean("active").notNull().default(true),
  createdBy: varchar("created_by").references(() => users.id),
});

export const storageVolumes = pgTable("storage_volumes", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  poolId: varchar("pool_id").references(() => storagePools.id).notNull(),
  sizeGB: integer("size_gb").notNull(),
  format: text("format").notNull(), // 'qcow2', 'raw', 'iso'
  vmId: varchar("vm_id").references(() => virtualMachines.id),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: varchar("created_by").references(() => users.id),
});

export const networks = pgTable("networks", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull().unique(),
  mode: text("mode").notNull(), // 'nat', 'bridge', 'isolated'
  ipRange: text("ip_range").notNull(),
  dhcpRange: text("dhcp_range"),
  bridge: text("bridge"),
  active: boolean("active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: varchar("created_by").references(() => users.id),
});

export const activities = pgTable("activities", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  message: text("message").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  type: text("type").notNull(), // 'info', 'success', 'warning', 'error'
  userId: varchar("user_id").references(() => users.id),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  hypervisors: many(hypervisors),
  virtualMachines: many(virtualMachines),
  sshKeys: many(sshKeys),
  sessions: many(sessions),
  userRoles: many(userRoles),
  auditLogs: many(auditLogs),
}));

export const hypervisorsRelations = relations(hypervisors, ({ one, many }) => ({
  createdBy: one(users, {
    fields: [hypervisors.createdBy],
    references: [users.id],
  }),
  virtualMachines: many(virtualMachines),
}));

export const virtualMachinesRelations = relations(virtualMachines, ({ one }) => ({
  hypervisor: one(hypervisors, {
    fields: [virtualMachines.hypervisorId],
    references: [hypervisors.id],
  }),
  createdBy: one(users, {
    fields: [virtualMachines.createdBy],
    references: [users.id],
  }),
}));

export const sshKeysRelations = relations(sshKeys, ({ one }) => ({
  user: one(users, {
    fields: [sshKeys.userId],
    references: [users.id],
  }),
}));

export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(users, {
    fields: [userRoles.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [userRoles.roleId],
    references: [roles.id],
  }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastLogin: true,
});

export const insertRoleSchema = createInsertSchema(roles).omit({
  id: true,
  createdAt: true,
});

export const insertSshKeySchema = createInsertSchema(sshKeys).omit({
  id: true,
  createdAt: true,
  lastUsed: true,
});

export const insertHypervisorSchema = createInsertSchema(hypervisors).omit({
  id: true,
  createdAt: true,
  lastSeen: true,
});

export const insertVirtualMachineSchema = createInsertSchema(virtualMachines).omit({
  id: true,
  createdAt: true,
});

export const insertStoragePoolSchema = createInsertSchema(storagePools).omit({
  id: true,
});

export const insertStorageVolumeSchema = createInsertSchema(storageVolumes).omit({
  id: true,
  createdAt: true,
});

export const insertNetworkSchema = createInsertSchema(networks).omit({
  id: true,
  createdAt: true,
});

// Type exports
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Role = typeof roles.$inferSelect;
export type InsertRole = z.infer<typeof insertRoleSchema>;
export type UserRole = typeof userRoles.$inferSelect;
export type SshKey = typeof sshKeys.$inferSelect;
export type InsertSshKey = z.infer<typeof insertSshKeySchema>;
export type Session = typeof sessions.$inferSelect;
export type AuditLog = typeof auditLogs.$inferSelect;

export const insertActivitySchema = createInsertSchema(activities).omit({
  id: true,
  timestamp: true,
});

// Types
export type Hypervisor = typeof hypervisors.$inferSelect;
export type InsertHypervisor = z.infer<typeof insertHypervisorSchema>;

export type VirtualMachine = typeof virtualMachines.$inferSelect;
export type InsertVirtualMachine = z.infer<typeof insertVirtualMachineSchema>;

export type StoragePool = typeof storagePools.$inferSelect;
export type InsertStoragePool = z.infer<typeof insertStoragePoolSchema>;

export type StorageVolume = typeof storageVolumes.$inferSelect;
export type InsertStorageVolume = z.infer<typeof insertStorageVolumeSchema>;

export type Network = typeof networks.$inferSelect;
export type InsertNetwork = z.infer<typeof insertNetworkSchema>;

export type Activity = typeof activities.$inferSelect;
export type InsertActivity = z.infer<typeof insertActivitySchema>;

// VM operation schemas
export const vmOperationSchema = z.object({
  action: z.enum(["start", "stop", "pause", "resume", "restart"]),
});

export type VmOperation = z.infer<typeof vmOperationSchema>;
