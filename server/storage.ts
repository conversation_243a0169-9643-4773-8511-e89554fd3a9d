import { 
  type Hypervisor,
  type InsertHypervisor,
  type VirtualMachine, 
  type InsertVirtualMachine,
  type StoragePool,
  type InsertStoragePool,
  type StorageVolume,
  type InsertStorageVolume,
  type Network,
  type InsertNetwork,
  type Activity,
  type InsertActivity
} from "@shared/schema";
import { randomUUID } from "crypto";

export interface IStorage {
  // Hypervisors
  getHypervisors(): Promise<Hypervisor[]>;
  getHypervisor(id: string): Promise<Hypervisor | undefined>;
  createHypervisor(hypervisor: InsertHypervisor): Promise<Hypervisor>;
  updateHypervisor(id: string, updates: Partial<Hypervisor>): Promise<Hypervisor | undefined>;
  deleteHypervisor(id: string): Promise<boolean>;

  // Virtual Machines
  getVirtualMachines(hypervisorId?: string): Promise<VirtualMachine[]>;
  getVirtualMachine(id: string): Promise<VirtualMachine | undefined>;
  createVirtualMachine(vm: InsertVirtualMachine): Promise<VirtualMachine>;
  updateVirtualMachine(id: string, updates: Partial<VirtualMachine>): Promise<VirtualMachine | undefined>;
  deleteVirtualMachine(id: string): Promise<boolean>;

  // Storage Pools
  getStoragePools(): Promise<StoragePool[]>;
  getStoragePool(id: string): Promise<StoragePool | undefined>;
  createStoragePool(pool: InsertStoragePool): Promise<StoragePool>;
  updateStoragePool(id: string, updates: Partial<StoragePool>): Promise<StoragePool | undefined>;

  // Storage Volumes
  getStorageVolumes(): Promise<StorageVolume[]>;
  getStorageVolumesByPool(poolId: string): Promise<StorageVolume[]>;
  createStorageVolume(volume: InsertStorageVolume): Promise<StorageVolume>;

  // Networks
  getNetworks(): Promise<Network[]>;
  getNetwork(id: string): Promise<Network | undefined>;
  createNetwork(network: InsertNetwork): Promise<Network>;
  updateNetwork(id: string, updates: Partial<Network>): Promise<Network | undefined>;

  // Activities
  getActivities(): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;
}

import { db } from "./db";
import { eq, and } from "drizzle-orm";
import { 
  hypervisors, 
  virtualMachines, 
  storagePools, 
  storageVolumes, 
  networks, 
  activities 
} from "@shared/schema";

export class DatabaseStorage implements IStorage {
  // Hypervisors
  async getHypervisors(): Promise<Hypervisor[]> {
    return await db.select().from(hypervisors);
  }

  async getHypervisor(id: string): Promise<Hypervisor | undefined> {
    const [hypervisor] = await db.select().from(hypervisors).where(eq(hypervisors.id, id));
    return hypervisor || undefined;
  }

  async createHypervisor(insertHypervisor: InsertHypervisor): Promise<Hypervisor> {
    const [hypervisor] = await db
      .insert(hypervisors)
      .values(insertHypervisor)
      .returning();
    return hypervisor;
  }

  async updateHypervisor(id: string, updates: Partial<Hypervisor>): Promise<Hypervisor | undefined> {
    const [hypervisor] = await db
      .update(hypervisors)
      .set(updates)
      .where(eq(hypervisors.id, id))
      .returning();
    return hypervisor || undefined;
  }

  async deleteHypervisor(id: string): Promise<boolean> {
    const result = await db.delete(hypervisors).where(eq(hypervisors.id, id));
    return result.rowCount > 0;
  }

  // Virtual Machines
  async getVirtualMachines(hypervisorId?: string): Promise<VirtualMachine[]> {
    if (hypervisorId) {
      return await db.select().from(virtualMachines).where(eq(virtualMachines.hypervisorId, hypervisorId));
    }
    return await db.select().from(virtualMachines);
  }

  async getVirtualMachine(id: string): Promise<VirtualMachine | undefined> {
    const [vm] = await db.select().from(virtualMachines).where(eq(virtualMachines.id, id));
    return vm || undefined;
  }

  async createVirtualMachine(insertVm: InsertVirtualMachine): Promise<VirtualMachine> {
    const [vm] = await db
      .insert(virtualMachines)
      .values(insertVm)
      .returning();
    return vm;
  }

  async updateVirtualMachine(id: string, updates: Partial<VirtualMachine>): Promise<VirtualMachine | undefined> {
    const [vm] = await db
      .update(virtualMachines)
      .set(updates)
      .where(eq(virtualMachines.id, id))
      .returning();
    return vm || undefined;
  }

  async deleteVirtualMachine(id: string): Promise<boolean> {
    const result = await db.delete(virtualMachines).where(eq(virtualMachines.id, id));
    return result.rowCount > 0;
  }

  // Storage Pools
  async getStoragePools(): Promise<StoragePool[]> {
    return await db.select().from(storagePools);
  }

  async getStoragePool(id: string): Promise<StoragePool | undefined> {
    const [pool] = await db.select().from(storagePools).where(eq(storagePools.id, id));
    return pool || undefined;
  }

  async createStoragePool(insertPool: InsertStoragePool): Promise<StoragePool> {
    const [pool] = await db
      .insert(storagePools)
      .values(insertPool)
      .returning();
    return pool;
  }

  async updateStoragePool(id: string, updates: Partial<StoragePool>): Promise<StoragePool | undefined> {
    const [pool] = await db
      .update(storagePools)
      .set(updates)
      .where(eq(storagePools.id, id))
      .returning();
    return pool || undefined;
  }

  // Storage Volumes
  async getStorageVolumes(): Promise<StorageVolume[]> {
    return await db.select().from(storageVolumes);
  }

  async getStorageVolumesByPool(poolId: string): Promise<StorageVolume[]> {
    return await db.select().from(storageVolumes).where(eq(storageVolumes.poolId, poolId));
  }

  async createStorageVolume(insertVolume: InsertStorageVolume): Promise<StorageVolume> {
    const [volume] = await db
      .insert(storageVolumes)
      .values(insertVolume)
      .returning();
    return volume;
  }

  // Networks
  async getNetworks(): Promise<Network[]> {
    return await db.select().from(networks);
  }

  async getNetwork(id: string): Promise<Network | undefined> {
    const [network] = await db.select().from(networks).where(eq(networks.id, id));
    return network || undefined;
  }

  async createNetwork(insertNetwork: InsertNetwork): Promise<Network> {
    const [network] = await db
      .insert(networks)
      .values(insertNetwork)
      .returning();
    return network;
  }

  async updateNetwork(id: string, updates: Partial<Network>): Promise<Network | undefined> {
    const [network] = await db
      .update(networks)
      .set(updates)
      .where(eq(networks.id, id))
      .returning();
    return network || undefined;
  }

  // Activities
  async getActivities(): Promise<Activity[]> {
    return await db.select().from(activities).orderBy(activities.timestamp);
  }

  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const [activity] = await db
      .insert(activities)
      .values(insertActivity)
      .returning();
    return activity;
  }
}

// Keep MemStorage for backward compatibility
export class MemStorage implements IStorage {
  private hypervisors: Map<string, Hypervisor> = new Map();
  private vms: Map<string, VirtualMachine> = new Map();
  private pools: Map<string, StoragePool> = new Map();
  private volumes: Map<string, StorageVolume> = new Map();
  private networks: Map<string, Network> = new Map();
  private activities: Map<string, Activity> = new Map();

  constructor() {
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Create sample hypervisors simulating a 400-node cluster
    const hypervisors = [];
    const vms = [];
    
    // Generate hypervisors for different racks/datacenters
    const locations = ["DC1-Rack01", "DC1-Rack02", "DC1-Rack03", "DC2-Rack01", "DC2-Rack02"];
    const baseHypervisors = [
      {
        name: "kvm-host-001",
        hostname: "kvm001.datacenter.local",
        ipAddress: "*********",
        status: "online",
        hypervisorType: "kvm",
        version: "6.2.0",
        cpuCores: 32,
        cpuUsage: 65,
        memoryGB: 128,
        memoryUsage: 70,
        totalVMs: 12,
        runningVMs: 10,
        location: "DC1-Rack01"
      },
      {
        name: "kvm-host-002",
        hostname: "kvm002.datacenter.local", 
        ipAddress: "*********",
        status: "online",
        hypervisorType: "kvm",
        version: "6.2.0",
        cpuCores: 32,
        cpuUsage: 45,
        memoryGB: 128,
        memoryUsage: 55,
        totalVMs: 8,
        runningVMs: 7,
        location: "DC1-Rack01"
      },
      {
        name: "kvm-host-003",
        hostname: "kvm003.datacenter.local",
        ipAddress: "*********", 
        status: "maintenance",
        hypervisorType: "kvm",
        version: "6.2.0",
        cpuCores: 32,
        cpuUsage: 0,
        memoryGB: 128,
        memoryUsage: 0,
        totalVMs: 0,
        runningVMs: 0,
        location: "DC1-Rack02"
      }
    ];

    // Generate multiple hypervisors
    for (let i = 0; i < Math.min(400, 20); i++) { // Limit to 20 for demo
      const template = baseHypervisors[i % baseHypervisors.length];
      const hypervisor = {
        id: randomUUID(),
        name: `kvm-host-${String(i + 1).padStart(3, '0')}`,
        hostname: `kvm${String(i + 1).padStart(3, '0')}.datacenter.local`,
        ipAddress: `10.${Math.floor(i / 254) + 1}.${Math.floor(i % 254 / 254) + 1}.${(i % 254) + 10}`,
        status: i % 15 === 0 ? "maintenance" : (i % 20 === 0 ? "offline" : "online"),
        hypervisorType: "kvm",
        version: "6.2.0",
        cpuCores: 32 + (i % 3) * 16, // 32, 48, or 64 cores
        cpuUsage: Math.floor(Math.random() * 80) + 10,
        memoryGB: 128 + (i % 4) * 64, // 128GB to 320GB
        memoryUsage: Math.floor(Math.random() * 70) + 20,
        totalVMs: Math.floor(Math.random() * 20) + 5,
        runningVMs: Math.floor(Math.random() * 15) + 2,
        location: locations[i % locations.length],
        lastSeen: new Date(Date.now() - Math.random() * 3600000), // Random last seen within 1 hour
        createdAt: new Date(Date.now() - Math.random() * 86400000 * 30), // Random creation within 30 days
      };
      hypervisors.push(hypervisor);
      this.hypervisors.set(hypervisor.id, hypervisor);
    }

    // Create sample VMs distributed across hypervisors
    const sampleVMs = [
      {
        name: "web-server-prod-01",
        os: "Ubuntu 22.04 LTS",
        status: "running",
        cpuCores: 4,
        cpuUsage: 45,
        memoryMB: 8192,
        memoryUsage: 60,
        diskGB: 100,
        diskUsage: 40,
        vmType: "guest",
        autostart: true,
      },
      {
        name: "database-primary",
        os: "Rocky Linux 9",
        status: "running",
        cpuCores: 8,
        cpuUsage: 75,
        memoryMB: 16384,
        memoryUsage: 80,
        diskGB: 500,
        diskUsage: 65,
        vmType: "guest",
        autostart: true,
      },
      {
        name: "dev-environment-001",
        os: "Windows 11 Pro",
        status: "paused",
        cpuCores: 4,
        cpuUsage: 0,
        memoryMB: 8192,
        memoryUsage: 0,
        diskGB: 60,
        diskUsage: 25,
        vmType: "guest",
        autostart: false,
      }
    ];

    hypervisors.slice(0, 10).forEach((hypervisor, hvIndex) => {
      for (let vmIndex = 0; vmIndex < Math.min(hypervisor.totalVMs, 8); vmIndex++) {
        const template = sampleVMs[vmIndex % sampleVMs.length];
        const vm = {
          id: randomUUID(),
          name: `${template.name.split('-')[0]}-${hvIndex + 1}-${vmIndex + 1}`,
          hypervisorId: hypervisor.id,
          os: template.os,
          status: vmIndex < hypervisor.runningVMs ? "running" : (Math.random() > 0.5 ? "stopped" : "paused"),
          cpuCores: template.cpuCores,
          cpuUsage: template.status === "running" ? template.cpuUsage : 0,
          memoryMB: template.memoryMB,
          memoryUsage: template.status === "running" ? template.memoryUsage : 0,
          diskGB: template.diskGB,
          diskUsage: template.diskUsage,
          vmType: template.vmType,
          uuid: randomUUID(),
          vnc_port: 5900 + (hvIndex * 100) + vmIndex,
          autostart: template.autostart,
          createdAt: new Date(Date.now() - Math.random() * 86400000 * 7), // Random creation within 7 days
        };
        vms.push(vm);
        this.vms.set(vm.id, vm);
      }
    });

    // Create sample storage pools
    const pools = [
      {
        id: randomUUID(),
        name: "default",
        type: "directory",
        path: "/var/lib/libvirt/images",
        capacityGB: 100,
        usedGB: 45,
        active: true,
      },
      {
        id: randomUUID(),
        name: "iso-images",
        type: "directory",
        path: "/var/lib/libvirt/iso",
        capacityGB: 50,
        usedGB: 13,
        active: true,
      },
    ];

    pools.forEach(pool => this.pools.set(pool.id, pool));

    // Create sample storage volumes
    const defaultPoolId = pools[0].id;
    const isoPoolId = pools[1].id;
    const volumes = [
      {
        id: randomUUID(),
        name: "ubuntu-server.qcow2",
        poolId: defaultPoolId,
        sizeGB: 20,
        format: "qcow2",
        vmId: vms[0].id,
      },
      {
        id: randomUUID(),
        name: "windows-dev.qcow2",
        poolId: defaultPoolId,
        sizeGB: 60,
        format: "qcow2",
        vmId: vms[1].id,
      },
      {
        id: randomUUID(),
        name: "centos-test.qcow2",
        poolId: defaultPoolId,
        sizeGB: 40,
        format: "qcow2",
        vmId: vms[2].id,
      },
      {
        id: randomUUID(),
        name: "ubuntu-22.04.iso",
        poolId: isoPoolId,
        sizeGB: 5,
        format: "iso",
        vmId: null,
      },
    ];

    volumes.forEach(volume => this.volumes.set(volume.id, volume));

    // Create sample networks
    const networks = [
      {
        id: randomUUID(),
        name: "default",
        mode: "nat",
        ipRange: "*************/24",
        dhcpRange: "************* - ***************",
        bridge: "virbr0",
        active: true,
      },
      {
        id: randomUUID(),
        name: "isolated",
        mode: "isolated",
        ipRange: "10.0.0.0/24",
        dhcpRange: "******** - **********",
        bridge: "virbr1",
        active: false,
      },
    ];

    networks.forEach(network => this.networks.set(network.id, network));

    // Create sample activities
    const activities = [
      {
        id: randomUUID(),
        message: 'VM "ubuntu-server" started successfully',
        timestamp: new Date(Date.now() - 2 * 60 * 1000),
        type: "success",
      },
      {
        id: randomUUID(),
        message: 'Created new VM "windows-dev"',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        type: "info",
      },
      {
        id: randomUUID(),
        message: 'VM "centos-test" paused by user',
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        type: "warning",
      },
    ];

    activities.forEach(activity => this.activities.set(activity.id, activity));
  }

  // Hypervisors
  async getHypervisors(): Promise<Hypervisor[]> {
    return Array.from(this.hypervisors.values());
  }

  async getHypervisor(id: string): Promise<Hypervisor | undefined> {
    return this.hypervisors.get(id);
  }

  async createHypervisor(insertHypervisor: InsertHypervisor): Promise<Hypervisor> {
    const id = randomUUID();
    const hypervisor: Hypervisor = {
      ...insertHypervisor,
      id,
      lastSeen: new Date(),
      createdAt: new Date(),
    };
    this.hypervisors.set(id, hypervisor);
    
    await this.createActivity({
      message: `Added new hypervisor "${hypervisor.name}"`,
      type: "info",
    });
    
    return hypervisor;
  }

  async updateHypervisor(id: string, updates: Partial<Hypervisor>): Promise<Hypervisor | undefined> {
    const hypervisor = this.hypervisors.get(id);
    if (!hypervisor) return undefined;
    
    const updatedHypervisor = { ...hypervisor, ...updates, lastSeen: new Date() };
    this.hypervisors.set(id, updatedHypervisor);
    
    return updatedHypervisor;
  }

  async deleteHypervisor(id: string): Promise<boolean> {
    const hypervisor = this.hypervisors.get(id);
    if (!hypervisor) return false;
    
    // Check if hypervisor has VMs
    const vmsOnHypervisor = Array.from(this.vms.values()).filter(vm => vm.hypervisorId === id);
    if (vmsOnHypervisor.length > 0) {
      throw new Error("Cannot delete hypervisor with existing VMs");
    }
    
    this.hypervisors.delete(id);
    
    await this.createActivity({
      message: `Removed hypervisor "${hypervisor.name}"`,
      type: "warning",
    });
    
    return true;
  }

  // Virtual Machines
  async getVirtualMachines(hypervisorId?: string): Promise<VirtualMachine[]> {
    const allVms = Array.from(this.vms.values());
    if (hypervisorId) {
      return allVms.filter(vm => vm.hypervisorId === hypervisorId);
    }
    return allVms;
  }

  async getVirtualMachine(id: string): Promise<VirtualMachine | undefined> {
    return this.vms.get(id);
  }

  async createVirtualMachine(insertVm: InsertVirtualMachine): Promise<VirtualMachine> {
    const id = randomUUID();
    const vm: VirtualMachine = {
      ...insertVm,
      id,
      createdAt: new Date(),
    };
    this.vms.set(id, vm);
    
    // Add activity
    await this.createActivity({
      message: `Created new VM "${vm.name}"`,
      type: "info",
    });
    
    return vm;
  }

  async updateVirtualMachine(id: string, updates: Partial<VirtualMachine>): Promise<VirtualMachine | undefined> {
    const vm = this.vms.get(id);
    if (!vm) return undefined;
    
    const updatedVm = { ...vm, ...updates };
    this.vms.set(id, updatedVm);
    
    return updatedVm;
  }

  async deleteVirtualMachine(id: string): Promise<boolean> {
    const vm = this.vms.get(id);
    if (!vm) return false;
    
    this.vms.delete(id);
    
    // Add activity
    await this.createActivity({
      message: `Deleted VM "${vm.name}"`,
      type: "warning",
    });
    
    return true;
  }

  // Storage Pools
  async getStoragePools(): Promise<StoragePool[]> {
    return Array.from(this.pools.values());
  }

  async getStoragePool(id: string): Promise<StoragePool | undefined> {
    return this.pools.get(id);
  }

  async createStoragePool(insertPool: InsertStoragePool): Promise<StoragePool> {
    const id = randomUUID();
    const pool: StoragePool = { ...insertPool, id };
    this.pools.set(id, pool);
    return pool;
  }

  async updateStoragePool(id: string, updates: Partial<StoragePool>): Promise<StoragePool | undefined> {
    const pool = this.pools.get(id);
    if (!pool) return undefined;
    
    const updatedPool = { ...pool, ...updates };
    this.pools.set(id, updatedPool);
    return updatedPool;
  }

  // Storage Volumes
  async getStorageVolumes(): Promise<StorageVolume[]> {
    return Array.from(this.volumes.values());
  }

  async getStorageVolumesByPool(poolId: string): Promise<StorageVolume[]> {
    return Array.from(this.volumes.values()).filter(volume => volume.poolId === poolId);
  }

  async createStorageVolume(insertVolume: InsertStorageVolume): Promise<StorageVolume> {
    const id = randomUUID();
    const volume: StorageVolume = { ...insertVolume, id };
    this.volumes.set(id, volume);
    return volume;
  }

  // Networks
  async getNetworks(): Promise<Network[]> {
    return Array.from(this.networks.values());
  }

  async getNetwork(id: string): Promise<Network | undefined> {
    return this.networks.get(id);
  }

  async createNetwork(insertNetwork: InsertNetwork): Promise<Network> {
    const id = randomUUID();
    const network: Network = { ...insertNetwork, id };
    this.networks.set(id, network);
    return network;
  }

  async updateNetwork(id: string, updates: Partial<Network>): Promise<Network | undefined> {
    const network = this.networks.get(id);
    if (!network) return undefined;
    
    const updatedNetwork = { ...network, ...updates };
    this.networks.set(id, updatedNetwork);
    return updatedNetwork;
  }

  // Activities
  async getActivities(): Promise<Activity[]> {
    return Array.from(this.activities.values())
      .sort((a, b) => b.timestamp!.getTime() - a.timestamp!.getTime());
  }

  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const id = randomUUID();
    const activity: Activity = {
      ...insertActivity,
      id,
      timestamp: new Date(),
    };
    this.activities.set(id, activity);
    return activity;
  }
}

export const storage = new DatabaseStorage();
