import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { db } from './db';
import { users, sessions, userRoles, roles, auditLogs } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const SALT_ROUNDS = 12;

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
  };
}

// Permission constants
export const PERMISSIONS = {
  // Hypervisor permissions
  HYPERVISOR_VIEW: 'hypervisor:view',
  HYPERVISOR_CREATE: 'hypervisor:create',
  HYPERVISOR_EDIT: 'hypervisor:edit',
  HYPERVISOR_DELETE: 'hypervisor:delete',
  HYPERVISOR_CONNECT: 'hypervisor:connect',
  
  // VM permissions
  VM_VIEW: 'vm:view',
  VM_CREATE: 'vm:create',
  VM_EDIT: 'vm:edit',
  VM_DELETE: 'vm:delete',
  VM_START: 'vm:start',
  VM_STOP: 'vm:stop',
  VM_RESTART: 'vm:restart',
  
  // Storage permissions
  STORAGE_VIEW: 'storage:view',
  STORAGE_CREATE: 'storage:create',
  STORAGE_EDIT: 'storage:edit',
  STORAGE_DELETE: 'storage:delete',
  
  // Network permissions
  NETWORK_VIEW: 'network:view',
  NETWORK_CREATE: 'network:create',
  NETWORK_EDIT: 'network:edit',
  NETWORK_DELETE: 'network:delete',
  
  // User management permissions
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  USER_MANAGE_ROLES: 'user:manage_roles',
  
  // SSH Key permissions
  SSH_KEY_VIEW: 'ssh_key:view',
  SSH_KEY_CREATE: 'ssh_key:create',
  SSH_KEY_EDIT: 'ssh_key:edit',
  SSH_KEY_DELETE: 'ssh_key:delete',
  
  // Admin permissions
  ADMIN_VIEW_LOGS: 'admin:view_logs',
  ADMIN_MANAGE_SYSTEM: 'admin:manage_system',
} as const;

// Default roles
export const DEFAULT_ROLES = {
  ADMIN: {
    name: 'Administrator',
    description: 'Full system access',
    permissions: Object.values(PERMISSIONS),
  },
  MANAGER: {
    name: 'Manager',
    description: 'Can manage VMs and infrastructure',
    permissions: [
      PERMISSIONS.HYPERVISOR_VIEW,
      PERMISSIONS.HYPERVISOR_CREATE,
      PERMISSIONS.HYPERVISOR_EDIT,
      PERMISSIONS.HYPERVISOR_CONNECT,
      PERMISSIONS.VM_VIEW,
      PERMISSIONS.VM_CREATE,
      PERMISSIONS.VM_EDIT,
      PERMISSIONS.VM_DELETE,
      PERMISSIONS.VM_START,
      PERMISSIONS.VM_STOP,
      PERMISSIONS.VM_RESTART,
      PERMISSIONS.STORAGE_VIEW,
      PERMISSIONS.STORAGE_CREATE,
      PERMISSIONS.STORAGE_EDIT,
      PERMISSIONS.NETWORK_VIEW,
      PERMISSIONS.NETWORK_CREATE,
      PERMISSIONS.NETWORK_EDIT,
      PERMISSIONS.SSH_KEY_VIEW,
      PERMISSIONS.SSH_KEY_CREATE,
      PERMISSIONS.SSH_KEY_EDIT,
      PERMISSIONS.SSH_KEY_DELETE,
    ],
  },
  OPERATOR: {
    name: 'Operator',
    description: 'Can operate VMs but not create/delete',
    permissions: [
      PERMISSIONS.HYPERVISOR_VIEW,
      PERMISSIONS.VM_VIEW,
      PERMISSIONS.VM_START,
      PERMISSIONS.VM_STOP,
      PERMISSIONS.VM_RESTART,
      PERMISSIONS.STORAGE_VIEW,
      PERMISSIONS.NETWORK_VIEW,
      PERMISSIONS.SSH_KEY_VIEW,
    ],
  },
  VIEWER: {
    name: 'Viewer',
    description: 'Read-only access',
    permissions: [
      PERMISSIONS.HYPERVISOR_VIEW,
      PERMISSIONS.VM_VIEW,
      PERMISSIONS.STORAGE_VIEW,
      PERMISSIONS.NETWORK_VIEW,
    ],
  },
};

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export function generateToken(userId: string): string {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
}

export function verifyToken(token: string): { userId: string } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: string };
  } catch {
    return null;
  }
}

export async function getUserWithRoles(userId: string) {
  const userWithRoles = await db
    .select({
      id: users.id,
      username: users.username,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      isActive: users.isActive,
      roleName: roles.name,
      permissions: roles.permissions,
    })
    .from(users)
    .leftJoin(userRoles, eq(userRoles.userId, users.id))
    .leftJoin(roles, eq(roles.id, userRoles.roleId))
    .where(eq(users.id, userId));

  if (!userWithRoles.length) return null;

  const user = userWithRoles[0];
  const roleNames = [...new Set(userWithRoles.map(ur => ur.roleName).filter(Boolean))];
  const allPermissions = userWithRoles
    .flatMap(ur => ur.permissions as string[] || [])
    .filter((permission, index, arr) => arr.indexOf(permission) === index);

  return {
    id: user.id,
    username: user.username,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    isActive: user.isActive,
    roles: roleNames,
    permissions: allPermissions,
  };
}

export async function logAuditEvent(
  userId: string | null,
  action: string,
  resourceType?: string,
  resourceId?: string,
  details?: any,
  req?: Request
) {
  await db.insert(auditLogs).values({
    userId,
    action,
    resourceType,
    resourceId,
    details,
    ipAddress: req?.ip,
    userAgent: req?.get('User-Agent'),
  });
}

// Authentication middleware
export async function authenticateToken(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({ error: 'Invalid or expired token' });
  }

  // Check if session exists and is valid
  const session = await db
    .select()
    .from(sessions)
    .where(and(
      eq(sessions.userId, decoded.userId),
      eq(sessions.token, token)
    ))
    .limit(1);

  if (!session.length || session[0].expiresAt < new Date()) {
    return res.status(401).json({ error: 'Session expired' });
  }

  const user = await getUserWithRoles(decoded.userId);
  if (!user || !user.isActive) {
    return res.status(401).json({ error: 'User not found or inactive' });
  }

  req.user = user;
  next();
}

// Permission middleware factory
export function requirePermission(permission: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!req.user.permissions.includes(permission)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: permission,
        userPermissions: req.user.permissions
      });
    }

    next();
  };
}

// Multiple permissions middleware (user must have ALL permissions)
export function requirePermissions(permissions: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const missingPermissions = permissions.filter(p => !req.user!.permissions.includes(p));
    if (missingPermissions.length > 0) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        missing: missingPermissions,
        userPermissions: req.user.permissions
      });
    }

    next();
  };
}

// Role-based middleware
export function requireRole(roleName: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!req.user.roles.includes(roleName)) {
      return res.status(403).json({ 
        error: 'Insufficient role',
        required: roleName,
        userRoles: req.user.roles
      });
    }

    next();
  };
}