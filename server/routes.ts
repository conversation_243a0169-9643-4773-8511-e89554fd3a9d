import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertHypervisorSchema,
  insertVirtualMachineSchema, 
  insertStoragePoolSchema,
  insertNetworkSchema,
  vmOperationSchema,
  insertUserSchema,
  insertSshKeySchema
} from "@shared/schema";
import { z } from "zod";
import { 
  hashPassword, 
  verifyPassword, 
  generateToken, 
  authenticateToken, 
  requirePermission, 
  PERMISSIONS,
  DEFAULT_ROLES,
  logAuditEvent,
  getUserWithRoles,
  type AuthenticatedRequest 
} from "./auth";
import { db } from "./db";
import { users, sessions, sshKeys, userRoles, roles } from "@shared/schema";
import { eq, and } from "drizzle-orm";
import crypto from "crypto";

// Helper functions
function generateSshFingerprint(publicKey: string): string {
  // Simple fingerprint generation for demo
  return crypto.createHash('md5').update(publicKey).digest('hex').match(/.{2}/g)?.join(':') || '';
}

async function initializeDefaultRolesAndAdmin() {
  try {
    // Create default roles if they don't exist
    for (const [roleKey, roleData] of Object.entries(DEFAULT_ROLES)) {
      const existingRole = await db
        .select()
        .from(roles)
        .where(eq(roles.name, roleData.name))
        .limit(1);

      if (!existingRole.length) {
        await db.insert(roles).values({
          name: roleData.name,
          description: roleData.description,
          permissions: roleData.permissions,
        });
      }
    }

    // Create default admin user if doesn't exist
    const existingAdmin = await db
      .select()
      .from(users)
      .where(eq(users.username, 'admin'))
      .limit(1);

    if (!existingAdmin.length) {
      const adminPasswordHash = await hashPassword('admin123');
      const [adminUser] = await db.insert(users).values({
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: adminPasswordHash,
        firstName: 'System',
        lastName: 'Administrator',
        isActive: true,
      }).returning();

      // Assign admin role
      const [adminRole] = await db
        .select()
        .from(roles)
        .where(eq(roles.name, DEFAULT_ROLES.ADMIN.name))
        .limit(1);

      if (adminRole) {
        await db.insert(userRoles).values({
          userId: adminUser.id,
          roleId: adminRole.id,
          assignedBy: adminUser.id,
        });
      }

      console.log('Default admin user created: admin / admin123');
    }
  } catch (error) {
    console.error('Error initializing default roles and admin:', error);
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize default roles and admin user
  await initializeDefaultRolesAndAdmin();

  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ error: "Username and password are required" });
      }

      // Find user
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.username, username))
        .limit(1);

      if (!user || !user.isActive) {
        await logAuditEvent(null, "login_failed", "user", null, { username, reason: "user_not_found" }, req);
        return res.status(401).json({ error: "Invalid credentials" });
      }

      // Verify password
      const validPassword = await verifyPassword(password, user.passwordHash);
      if (!validPassword) {
        await logAuditEvent(user.id, "login_failed", "user", user.id, { reason: "invalid_password" }, req);
        return res.status(401).json({ error: "Invalid credentials" });
      }

      // Generate token and create session
      const token = generateToken(user.id);
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      await db.insert(sessions).values({
        userId: user.id,
        token,
        expiresAt,
        ipAddress: req.ip,
        userAgent: req.get("User-Agent"),
      });

      // Update last login
      await db
        .update(users)
        .set({ lastLogin: new Date() })
        .where(eq(users.id, user.id));

      // Get user with roles
      const userWithRoles = await getUserWithRoles(user.id);

      await logAuditEvent(user.id, "login_success", "user", user.id, null, req);

      res.json({
        token,
        user: userWithRoles,
        expiresAt,
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  app.post("/api/auth/logout", authenticateToken, async (req: AuthenticatedRequest, res) => {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];

      if (token && req.user) {
        // Remove session
        await db
          .delete(sessions)
          .where(and(
            eq(sessions.userId, req.user.id),
            eq(sessions.token, token)
          ));

        await logAuditEvent(req.user.id, "logout", "user", req.user.id, null, req);
      }

      res.json({ message: "Logged out successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to logout" });
    }
  });

  app.get("/api/auth/user", authenticateToken, async (req: AuthenticatedRequest, res) => {
    res.json(req.user);
  });

  // SSH Keys management
  app.get("/api/auth/ssh-keys", authenticateToken, async (req: AuthenticatedRequest, res) => {
    try {
      const keys = await db
        .select()
        .from(sshKeys)
        .where(eq(sshKeys.userId, req.user!.id));

      res.json(keys);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch SSH keys" });
    }
  });

  app.post("/api/auth/ssh-keys", authenticateToken, async (req: AuthenticatedRequest, res) => {
    try {
      const keyData = insertSshKeySchema.parse({
        ...req.body,
        userId: req.user!.id,
        fingerprint: generateSshFingerprint(req.body.publicKey),
      });

      const [sshKey] = await db.insert(sshKeys).values(keyData).returning();

      await logAuditEvent(req.user!.id, "ssh_key_created", "ssh_key", sshKey.id, { name: keyData.name }, req);

      res.status(201).json(sshKey);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid SSH key data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create SSH key" });
    }
  });

  app.delete("/api/auth/ssh-keys/:id", authenticateToken, async (req: AuthenticatedRequest, res) => {
    try {
      const keyId = req.params.id;
      
      // Verify the key belongs to the user
      const [existingKey] = await db
        .select()
        .from(sshKeys)
        .where(and(
          eq(sshKeys.id, keyId),
          eq(sshKeys.userId, req.user!.id)
        ))
        .limit(1);

      if (!existingKey) {
        return res.status(404).json({ error: "SSH key not found" });
      }

      await db.delete(sshKeys).where(eq(sshKeys.id, keyId));

      await logAuditEvent(req.user!.id, "ssh_key_deleted", "ssh_key", keyId, { name: existingKey.name }, req);

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: "Failed to delete SSH key" });
    }
  });

  // Protected Hypervisors routes
  app.get("/api/hypervisors", authenticateToken, requirePermission(PERMISSIONS.HYPERVISOR_VIEW), async (req, res) => {
    try {
      const hypervisors = await storage.getHypervisors();
      res.json(hypervisors);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch hypervisors" });
    }
  });

  app.get("/api/hypervisors/:id", async (req, res) => {
    try {
      const hypervisor = await storage.getHypervisor(req.params.id);
      if (!hypervisor) {
        return res.status(404).json({ error: "Hypervisor not found" });
      }
      res.json(hypervisor);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch hypervisor" });
    }
  });

  app.post("/api/hypervisors", authenticateToken, requirePermission(PERMISSIONS.HYPERVISOR_CREATE), async (req: AuthenticatedRequest, res) => {
    try {
      const hypervisorData = insertHypervisorSchema.parse(req.body);
      const hypervisor = await storage.createHypervisor({
        ...hypervisorData,
        createdBy: req.user!.id
      });
      await logAuditEvent(req.user!.id, "hypervisor_created", "hypervisor", hypervisor.id, { name: hypervisor.name }, req);
      res.status(201).json(hypervisor);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid hypervisor data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create hypervisor" });
    }
  });

  app.delete("/api/hypervisors/:id", async (req, res) => {
    try {
      const deleted = await storage.deleteHypervisor(req.params.id);
      if (!deleted) {
        return res.status(404).json({ error: "Hypervisor not found" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: "Failed to delete hypervisor" });
    }
  });

  // OCI Integration - Scan instances
  app.post("/api/hypervisors/scan-oci", async (req, res) => {
    try {
      const ociConfig = req.body;
      
      // Mock OCI scanning for demonstration
      // In production, this would use the actual OCI SDK
      const mockInstances = [
        {
          id: "ocid1.instance.oc1.phx.example1",
          displayName: "web-server-phoenix-1",
          lifecycleState: "RUNNING",
          availabilityDomain: "PHX-AD-1",
          compartmentId: ociConfig.compartmentId,
          shape: {
            shape: "VM.Standard.E3.Flex",
            ocpus: 2,
            memoryInGBs: 32
          },
          publicIp: "**************",
          privateIp: "********"
        },
        {
          id: "ocid1.instance.oc1.phx.example2", 
          displayName: "database-server-phoenix",
          lifecycleState: "RUNNING",
          availabilityDomain: "PHX-AD-2",
          compartmentId: ociConfig.compartmentId,
          shape: {
            shape: "VM.Standard.E4.Flex",
            ocpus: 4,
            memoryInGBs: 64
          },
          publicIp: "**************",
          privateIp: "*********"
        },
        {
          id: "ocid1.instance.oc1.phx.example3",
          displayName: "dev-environment-1",
          lifecycleState: "STOPPED",
          availabilityDomain: "PHX-AD-1", 
          compartmentId: ociConfig.compartmentId,
          shape: {
            shape: "VM.Standard.E2.1",
            ocpus: 1,
            memoryInGBs: 8
          },
          publicIp: null,
          privateIp: "*********"
        }
      ];

      // Filter by region if specified
      const filteredInstances = ociConfig.region ? 
        mockInstances.filter(instance => instance.id.includes(ociConfig.region.split('-')[1])) :
        mockInstances;

      res.json({ 
        hosts: filteredInstances,
        message: `Found ${filteredInstances.length} compute instances in ${ociConfig.region || 'all regions'}`
      });
    } catch (error) {
      console.error("OCI scan error:", error);
      res.status(500).json({ error: "Failed to scan OCI instances" });
    }
  });

  // Test hypervisor connection
  app.post("/api/hypervisors/:id/test-connection", async (req, res) => {
    try {
      const hypervisor = await storage.getHypervisor(req.params.id);
      if (!hypervisor) {
        return res.status(404).json({ error: "Hypervisor not found" });
      }

      // Mock connection test
      const connectionResult = {
        success: Math.random() > 0.3, // 70% success rate for demo
        connectionType: hypervisor.connectionType,
        latency: Math.floor(Math.random() * 100) + 10,
        libvirtVersion: "7.6.0",
        error: Math.random() > 0.7 ? "SSH key authentication failed" : null
      };

      if (connectionResult.success) {
        // Update hypervisor status to online
        await storage.updateHypervisor(req.params.id, {
          status: "online",
          lastSeen: new Date(),
          version: connectionResult.libvirtVersion
        });
      }

      res.json(connectionResult);
    } catch (error) {
      res.status(500).json({ error: "Failed to test connection" });
    }
  });

  // Virtual Machines
  app.get("/api/vms", async (req, res) => {
    try {
      const hypervisorId = req.query.hypervisorId as string;
      const vms = await storage.getVirtualMachines(hypervisorId);
      res.json(vms);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch virtual machines" });
    }
  });

  app.get("/api/vms/:id", async (req, res) => {
    try {
      const vm = await storage.getVirtualMachine(req.params.id);
      if (!vm) {
        return res.status(404).json({ error: "Virtual machine not found" });
      }
      res.json(vm);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch virtual machine" });
    }
  });

  app.post("/api/vms", async (req, res) => {
    try {
      const vmData = insertVirtualMachineSchema.parse(req.body);
      const vm = await storage.createVirtualMachine(vmData);
      res.status(201).json(vm);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid VM data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create virtual machine" });
    }
  });

  app.post("/api/vms/:id/operations", async (req, res) => {
    try {
      const operation = vmOperationSchema.parse(req.body);
      const vm = await storage.getVirtualMachine(req.params.id);
      
      if (!vm) {
        return res.status(404).json({ error: "Virtual machine not found" });
      }

      let newStatus = vm.status;
      let cpuUsage = vm.cpuUsage;
      let memoryUsage = vm.memoryUsage;
      let activityMessage = "";

      switch (operation.action) {
        case "start":
          if (vm.status === "stopped") {
            newStatus = "running";
            cpuUsage = Math.floor(Math.random() * 50) + 20; // Random usage between 20-70%
            memoryUsage = Math.floor(Math.random() * 40) + 30; // Random usage between 30-70%
            activityMessage = `VM "${vm.name}" started successfully`;
          }
          break;
        case "stop":
          if (vm.status === "running" || vm.status === "paused") {
            newStatus = "stopped";
            cpuUsage = 0;
            memoryUsage = 0;
            activityMessage = `VM "${vm.name}" stopped`;
          }
          break;
        case "pause":
          if (vm.status === "running") {
            newStatus = "paused";
            cpuUsage = 0;
            activityMessage = `VM "${vm.name}" paused`;
          }
          break;
        case "resume":
          if (vm.status === "paused") {
            newStatus = "running";
            cpuUsage = Math.floor(Math.random() * 50) + 20;
            activityMessage = `VM "${vm.name}" resumed`;
          }
          break;
        case "restart":
          if (vm.status === "running") {
            newStatus = "running";
            cpuUsage = Math.floor(Math.random() * 50) + 20;
            memoryUsage = Math.floor(Math.random() * 40) + 30;
            activityMessage = `VM "${vm.name}" restarted`;
          }
          break;
      }

      const updatedVm = await storage.updateVirtualMachine(req.params.id, {
        status: newStatus,
        cpuUsage,
        memoryUsage,
      });

      if (activityMessage) {
        await storage.createActivity({
          message: activityMessage,
          type: "success",
        });
      }

      res.json(updatedVm);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid operation", details: error.errors });
      }
      res.status(500).json({ error: "Failed to perform VM operation" });
    }
  });

  app.delete("/api/vms/:id", async (req, res) => {
    try {
      const deleted = await storage.deleteVirtualMachine(req.params.id);
      if (!deleted) {
        return res.status(404).json({ error: "Virtual machine not found" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: "Failed to delete virtual machine" });
    }
  });

  // Storage Pools
  app.get("/api/storage/pools", async (req, res) => {
    try {
      const pools = await storage.getStoragePools();
      res.json(pools);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch storage pools" });
    }
  });

  app.post("/api/storage/pools", async (req, res) => {
    try {
      const poolData = insertStoragePoolSchema.parse(req.body);
      const pool = await storage.createStoragePool(poolData);
      res.status(201).json(pool);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid pool data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create storage pool" });
    }
  });

  // Storage Volumes
  app.get("/api/storage/volumes", async (req, res) => {
    try {
      const volumes = await storage.getStorageVolumes();
      res.json(volumes);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch storage volumes" });
    }
  });

  // Networks
  app.get("/api/networks", async (req, res) => {
    try {
      const networks = await storage.getNetworks();
      res.json(networks);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch networks" });
    }
  });

  app.post("/api/networks", async (req, res) => {
    try {
      const networkData = insertNetworkSchema.parse(req.body);
      const network = await storage.createNetwork(networkData);
      res.status(201).json(network);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid network data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create network" });
    }
  });

  // Activities
  app.get("/api/activities", async (req, res) => {
    try {
      const activities = await storage.getActivities();
      res.json(activities);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch activities" });
    }
  });

  // Dashboard stats
  app.get("/api/stats", async (req, res) => {
    try {
      const hypervisors = await storage.getHypervisors();
      const vms = await storage.getVirtualMachines();
      
      // Hypervisor stats
      const onlineHypervisors = hypervisors.filter(h => h.status === "online").length;
      const offlineHypervisors = hypervisors.filter(h => h.status === "offline").length;
      const maintenanceHypervisors = hypervisors.filter(h => h.status === "maintenance").length;
      
      // VM stats
      const runningVMs = vms.filter(vm => vm.status === "running").length;
      const pausedVMs = vms.filter(vm => vm.status === "paused").length;
      const stoppedVMs = vms.filter(vm => vm.status === "stopped").length;
      
      // Resource utilization across all hypervisors
      const totalCpuCores = hypervisors.reduce((acc, h) => acc + h.cpuCores, 0);
      const avgCpuUsage = onlineHypervisors > 0 
        ? Math.round(hypervisors.filter(h => h.status === "online").reduce((acc, h) => acc + h.cpuUsage, 0) / onlineHypervisors)
        : 0;
      
      const totalMemoryGB = hypervisors.reduce((acc, h) => acc + h.memoryGB, 0);
      const avgMemoryUsage = onlineHypervisors > 0
        ? Math.round(hypervisors.filter(h => h.status === "online").reduce((acc, h) => acc + h.memoryUsage, 0) / onlineHypervisors)
        : 0;

      res.json({
        hypervisors: {
          total: hypervisors.length,
          online: onlineHypervisors,
          offline: offlineHypervisors,
          maintenance: maintenanceHypervisors,
        },
        vms: {
          total: vms.length,
          running: runningVMs,
          paused: pausedVMs,
          stopped: stoppedVMs,
        },
        resources: {
          totalCpuCores,
          avgCpuUsage,
          totalMemoryGB,
          avgMemoryUsage,
        },
      });
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
